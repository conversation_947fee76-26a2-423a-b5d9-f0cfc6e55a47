<PERSON><PERSON> <<EMAIL>>
(no subject)
<PERSON> <<EMAIL>>	Sat, Jul 19, 2025 at 3:03 PM
To: <PERSON> <<EMAIL>>
Breath2 Debug Log - breath2_debug_2025-07-19_3-02-13 pm.log
Generated: 2025-07-19 12:03:11 pm +0000

================================================================================
BREATH2 DEBUG LOG SESSION
================================================================================
Start Time: 2025-07-19 12:02:13 pm +0000
Log File: breath2_debug_2025-07-19_3-02-13 pm.log
Purpose: Debug breath detection duration and pitch detection parameters
================================================================================
[2025-07-19T12:02:13Z] [INFO] Debug logging session started
DATA: {
"filename" : "breath2_debug_2025-07-19_3-02-13 pm.log"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:18Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"detectedPitch" : 0,
"calculatedPressure" : 0,
"audioLevel" : 0.0010748135
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:18Z] [BREATH] Breath detection analysis
DATA: {
"hasOscillation" : false,
"frequency" : 0,
"isBreathing" : false,
"amplitudeHistory" : [

],
"timestamp" : "2025-07-19T12:02:18Z",
"breathCount" : 0,
"currentState" : "idle",
"pressure" : 0,
"breathDuration" : 0,
"audioLevel" : 0.0010748135
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:18Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0014075513,
"frameCount" : 4800,
"detectedPitch" : 0,
"calculatedPressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:18Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"isBreathing" : false,
"breathCount" : 0,
"audioLevel" : 0.0014075513,
"timestamp" : "2025-07-19T12:02:18Z",
"breathDuration" : 0,
"amplitudeHistory" : [

],
"currentState" : "idle",
"pressure" : 0,
"hasOscillation" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:18Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.0012626979,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:18Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"isBreathing" : false,
"breathCount" : 0,
"audioLevel" : 0.0012626979,
"timestamp" : "2025-07-19T12:02:18Z",
"breathDuration" : 0,
"amplitudeHistory" : [

],
"currentState" : "idle",
"pressure" : 0,
"hasOscillation" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [AUDIO] Audio processing pipeline
DATA: {
"inputSamples" : 4800,
"averageAudioLevel" : 0.0003544436,
"squaredSamples" : 4800,
"minAudioLevel" : 6.691698e-08,
"timestamp" : "2025-07-19T12:02:19Z",
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"processingStepTimeMs" : {
"autocorrelation" : 0.19598007202148438,
"smoothing" : 0.12505054473876953,
"filtering" : 236.44602298736572,
"squaring" : 0.025033950805664062,
"downsampling" : 1.2799501419067383
},
"filteredSamples" : 4800,
"maxAudioLevel" : 0.0013276103
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"frameCount" : 4800,
"audioLevel" : 0.0013276103,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [BREATH] Breath detection analysis
DATA: {
"audioLevel" : 0.0013276103,
"currentState" : "idle",
"isBreathing" : false,
"amplitudeHistory" : [

],
"pressure" : 0,
"breathDuration" : 0,
"hasOscillation" : false,
"timestamp" : "2025-07-19T12:02:19Z",
"frequency" : 0,
"breathCount" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [AUDIO] Audio processing pipeline
DATA: {
"averageAudioLevel" : 0.00024311664,
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.0015261995,
"minAudioLevel" : 7.323251e-08,
"squaredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:19Z",
"inputSamples" : 4800,
"processingStepTimeMs" : {
"autocorrelation" : 0.23698806762695312,
"squaring" : 0.02396106719970703,
"smoothing" : 0.12302398681640625,
"filtering" : 236.26303672790527,
"downsampling" : 1.2780427932739258
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 0,
"audioLevel" : 0.0015261995,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"isBreathing" : false,
"hasOscillation" : false,
"currentState" : "idle",
"audioLevel" : 0.0015261995,
"breathDuration" : 0,
"amplitudeHistory" : [

],
"timestamp" : "2025-07-19T12:02:19Z",
"pressure" : 0,
"breathCount" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [AUDIO] Audio processing pipeline
DATA: {
"inputSamples" : 4800,
"filteredSamples" : 4800,
"maxAudioLevel" : 0.0013130382,
"minAudioLevel" : 9.567884e-08,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"squaring" : 0.025033950805664062,
"filtering" : 236.8159294128418,
"downsampling" : 1.2990236282348633,
"smoothing" : 0.12302398681640625,
"autocorrelation" : 0.19490718841552734
},
"squaredSamples" : 4800,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:19Z",
"averageAudioLevel" : 0.00026813726
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.0013130382,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [BREATH] Breath detection analysis
DATA: {
"currentState" : "idle",
"audioLevel" : 0.0013130382,
"timestamp" : "2025-07-19T12:02:19Z",
"isBreathing" : false,
"breathCount" : 0,
"hasOscillation" : false,
"frequency" : 0,
"amplitudeHistory" : [

],
"pressure" : 0,
"breathDuration" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"filtering" : 237.10298538208008,
"autocorrelation" : 0.19097328186035156,
"smoothing" : 0.12302398681640625,
"downsampling" : 1.267075538635254
},
"minAudioLevel" : 1.6944682e-07,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:19Z",
"maxAudioLevel" : 0.0011527939,
"squaredSamples" : 4800,
"smoothedSamples" : 97,
"averageAudioLevel" : 0.0002680983,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0011527939,
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [BREATH] Breath detection analysis
DATA: {
"currentState" : "idle",
"breathCount" : 0,
"frequency" : 0,
"pressure" : 0,
"hasOscillation" : false,
"audioLevel" : 0.0011527939,
"breathDuration" : 0,
"isBreathing" : false,
"timestamp" : "2025-07-19T12:02:19Z",
"amplitudeHistory" : [

]
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"autocorrelation" : 0.18990039825439453,
"filtering" : 236.2450361251831,
"downsampling" : 1.2699365615844727,
"squaring" : 0.012040138244628906,
"smoothing" : 0.1220703125
},
"maxAudioLevel" : 0.0013942439,
"averageAudioLevel" : 0.0003700733,
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:19Z",
"downsampledSamples" : 97,
"minAudioLevel" : 6.816117e-08,
"squaredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 0,
"frameCount" : 4800,
"calculatedPressure" : 0,
"audioLevel" : 0.0013942439
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:19Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"breathCount" : 0,
"amplitudeHistory" : [

],
"breathDuration" : 0,
"hasOscillation" : false,
"timestamp" : "2025-07-19T12:02:19Z",
"pressure" : 0,
"currentState" : "idle",
"isBreathing" : false,
"audioLevel" : 0.0013942439
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"filteredSamples" : 4800,
"maxAudioLevel" : 0.002227918,
"averageAudioLevel" : 0.0004676435,
"timestamp" : "2025-07-19T12:02:20Z",
"minAudioLevel" : 1.9351864e-07,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"smoothing" : 0.12505054473876953,
"autocorrelation" : 0.1989603042602539,
"squaring" : 0.012993812561035156,
"downsampling" : 1.284956932067871,
"filtering" : 239.12394046783447
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"frameCount" : 4800,
"audioLevel" : 0.002227918,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [BREATH] Breath detection analysis
DATA: {
"currentState" : "idle",
"isBreathing" : false,
"pressure" : 0,
"timestamp" : "2025-07-19T12:02:20Z",
"amplitudeHistory" : [

],
"hasOscillation" : false,
"frequency" : 0,
"breathCount" : 0,
"breathDuration" : 0,
"audioLevel" : 0.002227918
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0040420764,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:20Z",
"downsampledSamples" : 97,
"minAudioLevel" : 4.693793e-07,
"averageAudioLevel" : 0.0005990195,
"processingStepTimeMs" : {
"filtering" : 236.37795448303223,
"smoothing" : 0.13399124145507812,
"squaring" : 0.02300739288330078,
"downsampling" : 1.3020038604736328,
"autocorrelation" : 0.21600723266601562
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"audioLevel" : 0.0040420764,
"detectedPitch" : 0,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [BREATH] Breath detection analysis
DATA: {
"breathDuration" : 0,
"breathCount" : 0,
"isBreathing" : false,
"amplitudeHistory" : [

],
"audioLevel" : 0.0040420764,
"timestamp" : "2025-07-19T12:02:20Z",
"pressure" : 0,
"currentState" : "idle",
"frequency" : 0,
"hasOscillation" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"averageAudioLevel" : 0.00047887082,
"maxAudioLevel" : 0.005594708,
"processingStepTimeMs" : {
"smoothing" : 0.1329183578491211,
"downsampling" : 1.302957534790039,
"filtering" : 237.37609386444092,
"squaring" : 0.012993812561035156,
"autocorrelation" : 0.1920461654663086
},
"squaredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:20Z",
"downsampledSamples" : 97,
"minAudioLevel" : 5.229231e-07,
"filteredSamples" : 4800,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.005594708,
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [BREATH] Breath detection analysis
DATA: {
"timeSinceLastOscillation" : 0,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"breathDuration" : 0,
"breathCount" : 0,
"timestamp" : "2025-07-19T12:02:20Z",
"amplitudeHistory" : [
0.005
],
"audioLevel" : 0.005594708,
"hasOscillation" : true,
"pressure" : 0,
"frequency" : 0,
"isBreathing" : true,
"currentState" : "starting"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"averageAudioLevel" : 0.0005437579,
"maxAudioLevel" : 0.004357907,
"processingStepTimeMs" : {
"downsampling" : 1.2949705123901367,
"autocorrelation" : 0.19490718841552734,
"smoothing" : 0.12290477752685547,
"squaring" : 0.013947486877441406,
"filtering" : 237.2899055480957
},
"squaredSamples" : 4800,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:20Z",
"minAudioLevel" : 5.6363206e-08,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"frameCount" : 4800,
"detectedPitch" : 0,
"audioLevel" : 0.004357907
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:20Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"breathDuration" : 0.24558496475219727,
"timeSinceBreathStart" : 0.24558496475219727,
"audioLevel" : 0.004357907,
"breathCount" : 0,
"timeSinceLastOscillation" : 0.24558496475219727,
"currentState" : "starting",
"pressure" : 0,
"timestamp" : "2025-07-19T12:02:20Z",
"amplitudeHistory" : [
0.005
],
"frequency" : 0,
"hasOscillation" : false,
"isBreathing" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [AUDIO] Audio processing pipeline
DATA: {
"downsampledSamples" : 97,
"maxAudioLevel" : 0.0033947334,
"timestamp" : "2025-07-19T12:02:21Z",
"squaredSamples" : 4800,
"minAudioLevel" : 4.595495e-08,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"smoothing" : 0.1220703125,
"downsampling" : 1.271963119506836,
"autocorrelation" : 0.1919269561767578,
"filtering" : 238.89696598052979,
"squaring" : 0.012993812561035156
},
"filteredSamples" : 4800,
"averageAudioLevel" : 0.0003932921,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0033947334,
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "ending",
"hasOscillation" : false,
"breathDuration" : 0.49269700050354004,
"audioLevel" : 0.0033947334,
"timeSinceBreathStart" : 0.49269700050354004,
"amplitudeHistory" : [
0.005
],
"pressure" : 0,
"isBreathing" : true,
"timeSinceLastOscillation" : 0.49269700050354004,
"frequency" : 0,
"breathCount" : 0,
"timestamp" : "2025-07-19T12:02:21Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"averageAudioLevel" : 0.00035184843,
"maxAudioLevel" : 0.0013714669,
"processingStepTimeMs" : {
"downsampling" : 1.278996467590332,
"autocorrelation" : 0.1920461654663086,
"smoothing" : 0.12195110321044922,
"squaring" : 0.012993812561035156,
"filtering" : 236.84096336364746
},
"squaredSamples" : 4800,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:21Z",
"minAudioLevel" : 1.4443867e-07,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 0,
"calculatedPressure" : 0,
"frameCount" : 4800,
"audioLevel" : 0.0013714669
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"breathDuration" : 0,
"timeSinceBreathStart" : 0.7377740144729614,
"audioLevel" : 0.0013714669,
"breathCount" : 0,
"timeSinceLastOscillation" : 0.7377740144729614,
"currentState" : "completed",
"pressure" : 0,
"timestamp" : "2025-07-19T12:02:21Z",
"amplitudeHistory" : [

],
"frequency" : 0,
"hasOscillation" : false,
"isBreathing" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [AUDIO] Audio processing pipeline
DATA: {
"downsampledSamples" : 97,
"maxAudioLevel" : 0.001296852,
"timestamp" : "2025-07-19T12:02:21Z",
"squaredSamples" : 4800,
"minAudioLevel" : 6.167829e-08,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"squaring" : 0.011920928955078125,
"autocorrelation" : 0.1920461654663086,
"smoothing" : 0.12195110321044922,
"downsampling" : 1.2860298156738281,
"filtering" : 237.54000663757324
},
"filteredSamples" : 4800,
"averageAudioLevel" : 0.00033407,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.001296852,
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [BREATH] Breath detection analysis
DATA: {
"breathDuration" : 0,
"breathCount" : 0,
"isBreathing" : false,
"amplitudeHistory" : [

],
"audioLevel" : 0.001296852,
"timestamp" : "2025-07-19T12:02:21Z",
"pressure" : 0,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "idle",
"hasOscillation" : false,
"frequency" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [PITCH] Pitch detection analysis
DATA: {
"movingAveAmplitude" : 0.64941025,
"searchStrategy" : "full",
"targetMinFreq" : 13.862148,
"processingTimeMs" : 4.930019378662109,
"runLength" : 1,
"targetMaxFreq" : 23.10358,
"correlation" : 0.64941025,
"bestPeriod" : 53,
"audioLevel" : 0.00013602566,
"audioSamples" : 300,
"movingAvePeriod" : 53,
"timestamp" : "2025-07-19T12:02:21Z",
"audioVariance" : 5.9813005e-10,
"detectedPitch" : 18.482864,
"downsampledSamples" : 300,
"economicalSearchUsed" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [AUDIO] Audio processing pipeline
DATA: {
"downsampledSamples" : 97,
"maxAudioLevel" : 0.025340889,
"timestamp" : "2025-07-19T12:02:21Z",
"squaredSamples" : 4800,
"minAudioLevel" : 3.7590507e-07,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"autocorrelation" : 5.055069923400879,
"squaring" : 0.02396106719970703,
"filtering" : 236.5250587463379,
"downsampling" : 1.2929439544677734,
"smoothing" : 0.12195110321044922
},
"filteredSamples" : 4800,
"averageAudioLevel" : 0.0042093717,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 18.482864,
"calculatedPressure" : 16.023325,
"audioLevel" : 0.025340889,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:21Z] [BREATH] Breath detection analysis
DATA: {
"breathDuration" : 0,
"breathCount" : 0,
"isBreathing" : true,
"amplitudeHistory" : [
0.005
],
"audioLevel" : 0.025340889,
"pressure" : 16.023325,
"timestamp" : "2025-07-19T12:02:21Z",
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"timeSinceLastOscillation" : 0,
"frequency" : 18.482864,
"hasOscillation" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [PITCH] Pitch detection analysis
DATA: {
"correlation" : 0.66318303,
"detectedPitch" : 17.81076,
"downsampledSamples" : 300,
"bestPeriod" : 55,
"audioSamples" : 300,
"audioVariance" : 6.128455e-09,
"economicalSearchUsed" : true,
"movingAvePeriod" : 54,
"processingTimeMs" : 1.7609596252441406,
"targetMaxFreq" : 22.26345,
"audioLevel" : 0.00048845704,
"movingAveAmplitude" : 0.6562966,
"timestamp" : "2025-07-19T12:02:22Z",
"targetMinFreq" : 13.35807,
"searchStrategy" : "economical",
"runLength" : 2
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [AUDIO] Audio processing pipeline
DATA: {
"downsampledSamples" : 97,
"maxAudioLevel" : 0.048258632,
"timestamp" : "2025-07-19T12:02:22Z",
"squaredSamples" : 4800,
"minAudioLevel" : 2.7555798e-06,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"smoothing" : 0.1329183578491211,
"filtering" : 236.38999462127686,
"autocorrelation" : 1.885056495666504,
"squaring" : 0.02396106719970703,
"downsampling" : 1.2840032577514648
},
"filteredSamples" : 4800,
"averageAudioLevel" : 0.007914344,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.048258632,
"frameCount" : 4800,
"calculatedPressure" : 15.27124,
"detectedPitch" : 17.81076
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 0.2472679615020752,
"audioLevel" : 0.048258632,
"timeSinceBreathStart" : 0.2472679615020752,
"amplitudeHistory" : [
0.005,
1.0180827
],
"pressure" : 15.27124,
"isBreathing" : true,
"timeSinceLastOscillation" : 0,
"frequency" : 17.81076,
"breathCount" : 0,
"timestamp" : "2025-07-19T12:02:22Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [PITCH] Pitch detection analysis
DATA: {
"correlation" : 0.6365676,
"detectedPitch" : 16.603252,
"bestPeriod" : 59,
"downsampledSamples" : 300,
"audioVariance" : 9.235621e-09,
"audioSamples" : 300,
"economicalSearchUsed" : true,
"movingAvePeriod" : 55.666664,
"processingTimeMs" : 1.9268989562988281,
"targetMaxFreq" : 20.754066,
"audioLevel" : 0.0005274534,
"movingAveAmplitude" : 0.6497203,
"timestamp" : "2025-07-19T12:02:22Z",
"targetMinFreq" : 12.452439,
"searchStrategy" : "economical",
"runLength" : 3
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"averageAudioLevel" : 0.007822382,
"maxAudioLevel" : 0.04128325,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"filtering" : 238.91007900238037,
"downsampling" : 1.2750625610351562,
"autocorrelation" : 2.03096866607666,
"smoothing" : 0.12302398681640625
},
"squaredSamples" : 4800,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:22Z",
"minAudioLevel" : 7.931376e-07,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"detectedPitch" : 16.603252,
"calculatedPressure" : 13.920038,
"audioLevel" : 0.04128325
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 0.49532198905944824,
"audioLevel" : 0.04128325,
"timeSinceBreathStart" : 0.49532198905944824,
"amplitudeHistory" : [
0.005,
1.0180827,
0.92800254
],
"timeSinceLastOscillation" : 0,
"pressure" : 13.920038,
"frequency" : 16.603252,
"isBreathing" : true,
"breathCount" : 0,
"timestamp" : "2025-07-19T12:02:22Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [PITCH] Pitch detection analysis
DATA: {
"correlation" : 0.6092945,
"detectedPitch" : 14.620774,
"downsampledSamples" : 300,
"bestPeriod" : 67,
"audioSamples" : 300,
"audioVariance" : 1.0630595e-08,
"economicalSearchUsed" : true,
"movingAvePeriod" : 58.5,
"processingTimeMs" : 1.8949508666992188,
"targetMaxFreq" : 18.275969,
"audioLevel" : 0.0005274534,
"movingAveAmplitude" : 0.63961387,
"timestamp" : "2025-07-19T12:02:22Z",
"targetMinFreq" : 10.965581,
"searchStrategy" : "economical",
"runLength" : 4
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [AUDIO] Audio processing pipeline
DATA: {
"downsampledSamples" : 97,
"maxAudioLevel" : 0.038860634,
"timestamp" : "2025-07-19T12:02:22Z",
"squaredSamples" : 4800,
"minAudioLevel" : 2.2806926e-06,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"filtering" : 237.41793632507324,
"squaring" : 0.012993812561035156,
"downsampling" : 1.2929439544677734,
"smoothing" : 0.12195110321044922,
"autocorrelation" : 1.9989013671875
},
"filteredSamples" : 4800,
"averageAudioLevel" : 0.0069147293,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 11.701645,
"detectedPitch" : 14.620774,
"audioLevel" : 0.038860634
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 0.7428299188613892,
"audioLevel" : 0.038860634,
"timeSinceBreathStart" : 0.7428299188613892,
"amplitudeHistory" : [
0.005,
1.0180827,
0.92800254,
0.78010964
],
"pressure" : 11.701645,
"isBreathing" : true,
"timeSinceLastOscillation" : 0,
"frequency" : 14.620774,
"breathCount" : 0,
"timestamp" : "2025-07-19T12:02:22Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [PITCH] Pitch detection analysis
DATA: {
"correlation" : 0.62779886,
"detectedPitch" : 13.994169,
"bestPeriod" : 70,
"downsampledSamples" : 300,
"audioVariance" : 1.2877998e-08,
"audioSamples" : 300,
"economicalSearchUsed" : true,
"movingAvePeriod" : 60.8,
"processingTimeMs" : 2.1229982376098633,
"targetMaxFreq" : 17.492712,
"audioLevel" : 0.00066946144,
"movingAveAmplitude" : 0.63725084,
"timestamp" : "2025-07-19T12:02:22Z",
"targetMinFreq" : 10.495626,
"searchStrategy" : "economical",
"runLength" : 5
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"averageAudioLevel" : 0.008111451,
"maxAudioLevel" : 0.044956468,
"processingStepTimeMs" : {
"downsampling" : 1.2999773025512695,
"squaring" : 0.012040138244628906,
"filtering" : 236.67001724243164,
"smoothing" : 0.1220703125,
"autocorrelation" : 2.2319555282592773
},
"squaredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:22Z",
"downsampledSamples" : 97,
"minAudioLevel" : 1.8088322e-06,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 11.000475,
"detectedPitch" : 13.994169,
"audioLevel" : 0.044956468,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:22Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 0.9898339509963989,
"audioLevel" : 0.044956468,
"timeSinceBreathStart" : 0.9898339509963989,
"amplitudeHistory" : [
0.005,
1.0180827,
0.92800254,
0.78010964,
0.733365
],
"timeSinceLastOscillation" : 0,
"pressure" : 11.000475,
"frequency" : 13.994169,
"isBreathing" : true,
"breathCount" : 0,
"timestamp" : "2025-07-19T12:02:22Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [PITCH] Pitch detection analysis
DATA: {
"correlation" : 0.71222556,
"detectedPitch" : 8.90538,
"downsampledSamples" : 300,
"bestPeriod" : 110,
"audioSamples" : 300,
"audioVariance" : 1.2279492e-08,
"economicalSearchUsed" : false,
"movingAvePeriod" : 73.1,
"processingTimeMs" : 5.419015884399414,
"targetMaxFreq" : 11.131725,
"audioLevel" : 0.00066946144,
"movingAveAmplitude" : 0.65599453,
"timestamp" : "2025-07-19T12:02:23Z",
"targetMinFreq" : 7,
"searchStrategy" : "fallback",
"runLength" : 4
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [AUDIO] Audio processing pipeline
DATA: {
"downsampledSamples" : 97,
"maxAudioLevel" : 0.052425943,
"timestamp" : "2025-07-19T12:02:23Z",
"squaredSamples" : 4800,
"minAudioLevel" : 1.7581042e-06,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"smoothing" : 0.12099742889404297,
"autocorrelation" : 5.54203987121582,
"filtering" : 236.9070053100586,
"downsampling" : 1.292109489440918,
"squaring" : 0.011920928955078125
},
"filteredSamples" : 4800,
"averageAudioLevel" : 0.007871084,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 5.3061204,
"audioLevel" : 0.052425943,
"detectedPitch" : 8.90538
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 1.2403419017791748,
"audioLevel" : 0.052425943,
"timeSinceBreathStart" : 1.2403419017791748,
"amplitudeHistory" : [
0.005,
1.0180827,
0.92800254,
0.78010964,
0.733365,
0.35374135
],
"pressure" : 5.3061204,
"isBreathing" : true,
"timeSinceLastOscillation" : 0,
"frequency" : 8.90538,
"breathCount" : 0,
"timestamp" : "2025-07-19T12:02:23Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [PITCH] Pitch detection analysis
DATA: {
"correlation" : 0.7020195,
"detectedPitch" : 8.90538,
"downsampledSamples" : 300,
"bestPeriod" : 110,
"audioSamples" : 300,
"audioVariance" : 1.5068446e-08,
"economicalSearchUsed" : true,
"movingAvePeriod" : 82.325,
"processingTimeMs" : 2.7409791946411133,
"targetMaxFreq" : 11.131725,
"audioLevel" : 0.00069085386,
"movingAveAmplitude" : 0.6675008,
"timestamp" : "2025-07-19T12:02:23Z",
"targetMinFreq" : 7,
"searchStrategy" : "economical",
"runLength" : 4
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [AUDIO] Audio processing pipeline
DATA: {
"downsampledSamples" : 97,
"maxAudioLevel" : 0.050877597,
"timestamp" : "2025-07-19T12:02:23Z",
"squaredSamples" : 4800,
"minAudioLevel" : 8.242205e-07,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"filtering" : 236.4339828491211,
"autocorrelation" : 2.8510093688964844,
"downsampling" : 1.2819766998291016,
"smoothing" : 0.12195110321044922
},
"filteredSamples" : 4800,
"averageAudioLevel" : 0.008838001,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.050877597,
"frameCount" : 4800,
"detectedPitch" : 8.90538,
"calculatedPressure" : 5.3061204
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 1.4876859188079834,
"frequency" : 8.90538,
"pressure" : 5.3061204,
"amplitudeHistory" : [
0.005,
1.0180827,
0.92800254,
0.78010964,
0.733365,
0.35374135,
0.35374135
],
"timeSinceBreathStart" : 1.4876859188079834,
"timeSinceLastOscillation" : 0,
"audioLevel" : 0.050877597,
"isBreathing" : true,
"breathCount" : 0,
"timestamp" : "2025-07-19T12:02:23Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [PITCH] Pitch detection analysis
DATA: {
"correlation" : 0.75643665,
"detectedPitch" : 9.070295,
"downsampledSamples" : 300,
"bestPeriod" : 108,
"audioSamples" : 300,
"audioVariance" : 1.7490589e-08,
"economicalSearchUsed" : true,
"movingAvePeriod" : 88.743744,
"processingTimeMs" : 2.7610063552856445,
"targetMaxFreq" : 11.33787,
"audioLevel" : 0.0007721549,
"movingAveAmplitude" : 0.68973476,
"timestamp" : "2025-07-19T12:02:23Z",
"targetMinFreq" : 7,
"searchStrategy" : "economical",
"runLength" : 4
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [AUDIO] Audio processing pipeline
DATA: {
"downsampledSamples" : 97,
"maxAudioLevel" : 0.051394723,
"timestamp" : "2025-07-19T12:02:23Z",
"squaredSamples" : 4800,
"minAudioLevel" : 2.7611386e-06,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"downsampling" : 1.2860298156738281,
"squaring" : 0.02396106719970703,
"smoothing" : 0.1310110092163086,
"filtering" : 237.79499530792236,
"autocorrelation" : 2.8640031814575195
},
"filteredSamples" : 4800,
"averageAudioLevel" : 0.009102149,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 9.070295,
"audioLevel" : 0.051394723,
"calculatedPressure" : 5.49066,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"currentState" : "active",
"hasOscillation" : true,
"breathDuration" : 1.7364859580993652,
"audioLevel" : 0.051394723,
"timeSinceBreathStart" : 1.7364859580993652,
"amplitudeHistory" : [
0.005,
1.0180827,
0.92800254,
0.78010964,
0.733365,
0.35374135,
0.35374135,
0.366044
],
"timeSinceLastOscillation" : 0,
"pressure" : 5.49066,
"isBreathing" : true,
"frequency" : 9.070295,
"timestamp" : "2025-07-19T12:02:23Z",
"breathCount" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [PITCH] Pitch detection analysis
DATA: {
"downsampledSamples" : 300,
"detectedPitch" : 8.987082,
"audioVariance" : 1.633179e-08,
"correlation" : 0.76920176,
"audioSamples" : 300,
"bestPeriod" : 109,
"economicalSearchUsed" : true,
"movingAvePeriod" : 92.795,
"processingTimeMs" : 2.7370452880859375,
"targetMaxFreq" : 11.233852,
"audioLevel" : 0.0007721549,
"movingAveAmplitude" : 0.70562816,
"timestamp" : "2025-07-19T12:02:23Z",
"targetMinFreq" : 7,
"searchStrategy" : "economical",
"runLength" : 5
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.050692774,
"smoothedSamples" : 97,
"inputSamples" : 4800,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:23Z",
"minAudioLevel" : 7.029623e-06,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"averageAudioLevel" : 0.008574755,
"processingStepTimeMs" : {
"filtering" : 236.51409149169922,
"smoothing" : 0.12195110321044922,
"squaring" : 0.012993812561035156,
"downsampling" : 1.3039112091064453,
"autocorrelation" : 2.8409957885742188
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 8.987082,
"calculatedPressure" : 5.3975444,
"audioLevel" : 0.050692774,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:23Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"currentState" : "active",
"hasOscillation" : true,
"breathDuration" : 1.9839649200439453,
"frequency" : 8.987082,
"audioLevel" : 0.050692774,
"timeSinceBreathStart" : 1.9839649200439453,
"timeSinceLastOscillation" : 0,
"amplitudeHistory" : [
0.005,
1.0180827,
0.92800254,
0.78010964,
0.733365,
0.35374135,
0.35374135,
0.366044,
0.35983628
],
"isBreathing" : true,
"pressure" : 5.3975444,
"timestamp" : "2025-07-19T12:02:23Z",
"breathCount" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [PITCH] Pitch detection analysis
DATA: {
"movingAveAmplitude" : 0.70623845,
"searchStrategy" : "economical",
"targetMinFreq" : 7,
"processingTimeMs" : 2.707958221435547,
"runLength" : 5,
"targetMaxFreq" : 11.33787,
"correlation" : 0.7086796,
"bestPeriod" : 108,
"audioLevel" : 0.0007721549,
"audioSamples" : 300,
"movingAvePeriod" : 95.836,
"timestamp" : "2025-07-19T12:02:24Z",
"audioVariance" : 1.3908707e-08,
"downsampledSamples" : 300,
"detectedPitch" : 9.070295,
"economicalSearchUsed" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [AUDIO] Audio processing pipeline
DATA: {
"filteredSamples" : 4800,
"processingStepTimeMs" : {
"smoothing" : 0.12290477752685547,
"filtering" : 236.98198795318604,
"downsampling" : 1.2780427932739258,
"autocorrelation" : 2.810955047607422,
"squaring" : 0.012993812561035156
},
"maxAudioLevel" : 0.050077908,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:24Z",
"minAudioLevel" : 1.6268896e-06,
"averageAudioLevel" : 0.009393149,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.050077908,
"detectedPitch" : 9.070295,
"calculatedPressure" : 5.49066,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [BREATH] Breath detection analysis
DATA: {
"amplitudeHistory" : [
0.005,
1.0180827,
0.92800254,
0.78010964,
0.733365,
0.35374135,
0.35374135,
0.366044,
0.35983628,
0.366044
],
"isBreathing" : true,
"breathCount" : 0,
"frequency" : 9.070295,
"audioLevel" : 0.050077908,
"currentState" : "active",
"timestamp" : "2025-07-19T12:02:24Z",
"timeSinceBreathStart" : 2.231822967529297,
"hasOscillation" : true,
"breathDuration" : 2.231822967529297,
"timeSinceLastOscillation" : 0,
"pressure" : 5.49066,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 2.704024314880371,
"economicalSearchUsed" : true,
"correlation" : 0.7033188,
"audioLevel" : 0.000747857,
"downsampledSamples" : 300,
"timestamp" : "2025-07-19T12:02:24Z",
"detectedPitch" : 9.155064,
"targetMaxFreq" : 11.44383,
"searchStrategy" : "economical",
"movingAvePeriod" : 98.0688,
"runLength" : 5,
"targetMinFreq" : 7,
"audioVariance" : 1.2408618e-08,
"audioSamples" : 300,
"bestPeriod" : 107,
"movingAveAmplitude" : 0.7056545
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.04026331,
"smoothedSamples" : 97,
"timestamp" : "2025-07-19T12:02:24Z",
"inputSamples" : 4800,
"downsampledSamples" : 97,
"minAudioLevel" : 1.7564744e-06,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"averageAudioLevel" : 0.008907456,
"processingStepTimeMs" : {
"autocorrelation" : 2.8020143508911133,
"downsampling" : 1.2830495834350586,
"filtering" : 238.2880449295044,
"squaring" : 0.013113021850585938,
"smoothing" : 0.12600421905517578
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 5.5855155,
"frameCount" : 4800,
"audioLevel" : 0.04026331,
"detectedPitch" : 9.155064
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"timeSinceBreathStart" : 2.481572985649109,
"amplitudeHistory" : [
1.0180827,
0.92800254,
0.78010964,
0.733365,
0.35374135,
0.35374135,
0.366044,
0.35983628,
0.366044,
0.3723677
],
"pressure" : 5.5855155,
"currentState" : "active",
"timestamp" : "2025-07-19T12:02:24Z",
"audioLevel" : 0.04026331,
"hasOscillation" : true,
"breathDuration" : 2.481572985649109,
"breathCount" : 0,
"frequency" : 9.155064,
"timeSinceLastOscillation" : 0,
"isBreathing" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [PITCH] Pitch detection analysis
DATA: {
"bestPeriod" : 107,
"targetMaxFreq" : 11.44383,
"targetMinFreq" : 7,
"searchStrategy" : "economical",
"timestamp" : "2025-07-19T12:02:24Z",
"audioLevel" : 0.00054230884,
"correlation" : 0.6795682,
"audioVariance" : 1.2299991e-08,
"processingTimeMs" : 2.7730464935302734,
"economicalSearchUsed" : true,
"movingAvePeriod" : 99.85504,
"movingAveAmplitude" : 0.70043725,
"audioSamples" : 300,
"downsampledSamples" : 300,
"detectedPitch" : 9.155064,
"runLength" : 5
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"smoothing" : 0.12195110321044922,
"downsampling" : 1.2919902801513672,
"filtering" : 238.69705200195312,
"squaring" : 0.014066696166992188,
"autocorrelation" : 2.871990203857422
},
"maxAudioLevel" : 0.048787303,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:24Z",
"minAudioLevel" : 4.9441587e-06,
"averageAudioLevel" : 0.009829973,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 5.5855155,
"detectedPitch" : 9.155064,
"audioLevel" : 0.048787303
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : true,
"frequency" : 9.155064,
"currentState" : "active",
"audioLevel" : 0.048787303,
"timestamp" : "2025-07-19T12:02:24Z",
"breathCount" : 0,
"timeSinceBreathStart" : 2.7308489084243774,
"breathDuration" : 2.7308489084243774,
"hasOscillation" : true,
"pressure" : 5.5855155,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"amplitudeHistory" : [
0.92800254,
0.78010964,
0.733365,
0.35374135,
0.35374135,
0.366044,
0.35983628,
0.366044,
0.3723677,
0.3723677
],
"timeSinceLastOscillation" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [PITCH] Pitch detection analysis
DATA: {
"runLength" : 5,
"searchStrategy" : "economical",
"downsampledSamples" : 300,
"movingAveAmplitude" : 0.68960404,
"audioLevel" : 0.00054230884,
"timestamp" : "2025-07-19T12:02:24Z",
"bestPeriod" : 105,
"correlation" : 0.64627105,
"processingTimeMs" : 2.7849674224853516,
"movingAvePeriod" : 100.88403,
"audioVariance" : 1.1356072e-08,
"targetMaxFreq" : 11.661807,
"targetMinFreq" : 7,
"audioSamples" : 300,
"detectedPitch" : 9.329446,
"economicalSearchUsed" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [AUDIO] Audio processing pipeline
DATA: {
"filteredSamples" : 4800,
"processingStepTimeMs" : {
"filtering" : 237.09893226623535,
"smoothing" : 0.12600421905517578,
"autocorrelation" : 2.897977828979492,
"squaring" : 0.024080276489257812,
"downsampling" : 1.3009309768676758
},
"maxAudioLevel" : 0.043982167,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:24Z",
"minAudioLevel" : 4.362899e-06,
"averageAudioLevel" : 0.009445924,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 9.329446,
"frameCount" : 4800,
"audioLevel" : 0.043982167,
"calculatedPressure" : 5.7806497
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:24Z] [BREATH] Breath detection analysis
DATA: {
"pressure" : 5.7806497,
"hasOscillation" : true,
"timestamp" : "2025-07-19T12:02:24Z",
"audioLevel" : 0.043982167,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"amplitudeHistory" : [
0.78010964,
0.733365,
0.35374135,
0.35374135,
0.366044,
0.35983628,
0.366044,
0.3723677,
0.3723677,
0.38537663
],
"frequency" : 9.329446,
"isBreathing" : true,
"currentState" : "active",
"breathCount" : 0,
"breathDuration" : 2.9790199995040894,
"timeSinceLastOscillation" : 0,
"timeSinceBreathStart" : 2.9790199995040894
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [PITCH] Pitch detection analysis
DATA: {
"correlation" : 0.6753135,
"targetMaxFreq" : 11.88825,
"searchStrategy" : "economical",
"bestPeriod" : 103,
"movingAvePeriod" : 101.30723,
"economicalSearchUsed" : true,
"timestamp" : "2025-07-19T12:02:25Z",
"processingTimeMs" : 2.704024314880371,
"targetMinFreq" : 7.13295,
"movingAveAmplitude" : 0.68674594,
"audioLevel" : 0.0006661174,
"audioSamples" : 300,
"downsampledSamples" : 300,
"audioVariance" : 1.2351156e-08,
"runLength" : 5,
"detectedPitch" : 9.5106
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [AUDIO] Audio processing pipeline
DATA: {
"downsampledSamples" : 97,
"maxAudioLevel" : 0.048568785,
"timestamp" : "2025-07-19T12:02:25Z",
"squaredSamples" : 4800,
"minAudioLevel" : 2.1236629e-06,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"smoothing" : 0.12302398681640625,
"filtering" : 236.96792125701904,
"squaring" : 0.02491474151611328,
"autocorrelation" : 2.807021141052246,
"downsampling" : 1.2869834899902344
},
"filteredSamples" : 4800,
"averageAudioLevel" : 0.009437841,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 5.983361,
"audioLevel" : 0.048568785,
"detectedPitch" : 9.5106,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [BREATH] Breath detection analysis
DATA: {
"timeSinceBreathStart" : 3.226870894432068,
"frequency" : 9.5106,
"hasOscillation" : true,
"timestamp" : "2025-07-19T12:02:25Z",
"breathDuration" : 3.226870894432068,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"isBreathing" : true,
"currentState" : "active",
"breathCount" : 0,
"timeSinceLastOscillation" : 0,
"amplitudeHistory" : [
0.733365,
0.35374135,
0.35374135,
0.366044,
0.35983628,
0.366044,
0.3723677,
0.3723677,
0.38537663,
0.3988907
],
"pressure" : 5.983361,
"audioLevel" : 0.048568785
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [PITCH] Pitch detection analysis
DATA: {
"targetMinFreq" : 7,
"detectedPitch" : 9.241432,
"economicalSearchUsed" : true,
"bestPeriod" : 106,
"timestamp" : "2025-07-19T12:02:25Z",
"targetMaxFreq" : 11.55179,
"audioSamples" : 300,
"movingAveAmplitude" : 0.6891157,
"movingAvePeriod" : 102.24578,
"runLength" : 5,
"searchStrategy" : "economical",
"audioVariance" : 9.022722e-09,
"processingTimeMs" : 2.6509761810302734,
"audioLevel" : 0.0006661174,
"downsampledSamples" : 300,
"correlation" : 0.6985947
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 3.178604e-06,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"smoothing" : 0.12302398681640625,
"filtering" : 236.89603805541992,
"squaring" : 0.012993812561035156,
"autocorrelation" : 2.7559995651245117,
"downsampling" : 1.2680292129516602
},
"inputSamples" : 4800,
"filteredSamples" : 4800,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.007423562,
"maxAudioLevel" : 0.04059975,
"timestamp" : "2025-07-19T12:02:25Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 5.682163,
"frameCount" : 4800,
"detectedPitch" : 9.241432,
"audioLevel" : 0.04059975
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [BREATH] Breath detection analysis
DATA: {
"pressure" : 5.682163,
"hasOscillation" : true,
"timestamp" : "2025-07-19T12:02:25Z",
"audioLevel" : 0.04059975,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"amplitudeHistory" : [
0.35374135,
0.35374135,
0.366044,
0.35983628,
0.366044,
0.3723677,
0.3723677,
0.38537663,
0.3988907,
0.37881085
],
"frequency" : 9.241432,
"isBreathing" : true,
"currentState" : "active",
"breathCount" : 0,
"breathDuration" : 3.4745609760284424,
"timeSinceLastOscillation" : 0,
"timeSinceBreathStart" : 3.4745609760284424
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [PITCH] Pitch detection analysis
DATA: {
"detectedPitch" : 19.207684,
"targetMaxFreq" : 24.009605,
"searchStrategy" : "fallback",
"bestPeriod" : 51,
"movingAvePeriod" : 0,
"economicalSearchUsed" : false,
"timestamp" : "2025-07-19T12:02:25Z",
"processingTimeMs" : 5.9310197830200195,
"targetMinFreq" : 14.405763,
"movingAveAmplitude" : 0,
"audioLevel" : 0.0006661174,
"audioSamples" : 300,
"downsampledSamples" : 300,
"audioVariance" : 7.288435e-09,
"runLength" : 0,
"correlation" : 0.6641694
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 1.7891216e-07,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"filtering" : 237.4969720840454,
"downsampling" : 1.273036003112793,
"smoothing" : 0.1239776611328125,
"autocorrelation" : 6.038069725036621
},
"inputSamples" : 4800,
"filteredSamples" : 4800,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.00037271387,
"maxAudioLevel" : 0.0016536961,
"timestamp" : "2025-07-19T12:02:25Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 19.207684,
"calculatedPressure" : 16.834396,
"frameCount" : 4800,
"audioLevel" : 0.0016536961
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"timeSinceBreathStart" : 3.7261229753494263,
"amplitudeHistory" : [
0.35374135,
0.366044,
0.35983628,
0.366044,
0.3723677,
0.3723677,
0.38537663,
0.3988907,
0.37881085,
1.1222931
],
"currentState" : "active",
"pressure" : 16.834396,
"timestamp" : "2025-07-19T12:02:25Z",
"audioLevel" : 0.0016536961,
"hasOscillation" : true,
"breathDuration" : 3.7261229753494263,
"breathCount" : 0,
"frequency" : 19.207684,
"isBreathing" : true,
"timeSinceLastOscillation" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [PITCH] Pitch detection analysis
DATA: {
"bestPeriod" : 57,
"targetMaxFreq" : 21.482277,
"targetMinFreq" : 12.889366,
"searchStrategy" : "economical",
"timestamp" : "2025-07-19T12:02:25Z",
"audioLevel" : 0.00038157232,
"correlation" : 0.697454,
"audioVariance" : 2.7527614e-09,
"processingTimeMs" : 1.7549991607666016,
"economicalSearchUsed" : true,
"movingAvePeriod" : 57,
"movingAveAmplitude" : 0.697454,
"audioSamples" : 300,
"downsampledSamples" : 300,
"detectedPitch" : 17.185822,
"runLength" : 1
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [AUDIO] Audio processing pipeline
DATA: {
"filteredSamples" : 4800,
"processingStepTimeMs" : {
"smoothing" : 0.12803077697753906,
"filtering" : 236.73808574676514,
"squaring" : 0.012040138244628906,
"autocorrelation" : 1.8669366836547852,
"downsampling" : 1.2860298156738281
},
"maxAudioLevel" : 0.0013567589,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:25Z",
"minAudioLevel" : 5.467882e-09,
"averageAudioLevel" : 0.0002959039,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"audioLevel" : 0.0013567589,
"detectedPitch" : 17.185822,
"calculatedPressure" : 14.571934
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:25Z] [BREATH] Breath detection analysis
DATA: {
"amplitudeHistory" : [
0.366044,
0.35983628,
0.366044,
0.3723677,
0.3723677,
0.38537663,
0.3988907,
0.37881085,
1.1222931,
0.97146225
],
"isBreathing" : true,
"breathCount" : 0,
"frequency" : 17.185822,
"audioLevel" : 0.0013567589,
"currentState" : "active",
"timestamp" : "2025-07-19T12:02:25Z",
"timeSinceBreathStart" : 3.9728379249572754,
"hasOscillation" : true,
"breathDuration" : 3.9728379249572754,
"timeSinceLastOscillation" : 0,
"pressure" : 14.571934,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 4.556059837341309,
"economicalSearchUsed" : false,
"correlation" : 0.39895064,
"audioLevel" : 0.000118372,
"downsampledSamples" : 300,
"detectedPitch" : 0,
"timestamp" : "2025-07-19T12:02:26Z",
"targetMaxFreq" : 21.482277,
"searchStrategy" : "fallback",
"movingAvePeriod" : 57,
"runLength" : 1,
"targetMinFreq" : 12.889366,
"bestPeriod" : 139,
"audioSamples" : 300,
"audioVariance" : 1.1347139e-10,
"movingAveAmplitude" : 0.697454
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 3.2412208e-07,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"smoothing" : 0.12099742889404297,
"downsampling" : 1.2840032577514648,
"filtering" : 236.48405075073242,
"autocorrelation" : 4.647970199584961,
"squaring" : 0.012993812561035156
},
"inputSamples" : 4800,
"downsampledSamples" : 97,
"filteredSamples" : 4800,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.0003125306,
"maxAudioLevel" : 0.0013531342,
"timestamp" : "2025-07-19T12:02:26Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0013531342,
"detectedPitch" : 0,
"calculatedPressure" : 0,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"currentState" : "active",
"hasOscillation" : false,
"breathDuration" : 4.222071886062622,
"frequency" : 0,
"audioLevel" : 0.0013531342,
"amplitudeHistory" : [
0.366044,
0.35983628,
0.366044,
0.3723677,
0.3723677,
0.38537663,
0.3988907,
0.37881085,
1.1222931,
0.97146225
],
"timeSinceLastOscillation" : 0.24923396110534668,
"isBreathing" : true,
"pressure" : 0,
"timeSinceBreathStart" : 4.222071886062622,
"timestamp" : "2025-07-19T12:02:26Z",
"breathCount" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"smoothing" : 0.12099742889404297,
"downsampling" : 1.2840032577514648,
"filtering" : 236.9009256362915,
"squaring" : 0.012993812561035156,
"autocorrelation" : 0.2009868621826172
},
"maxAudioLevel" : 0.0012989747,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:26Z",
"minAudioLevel" : 7.128301e-08,
"averageAudioLevel" : 0.00039358067,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.0012989747
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "ending",
"hasOscillation" : false,
"breathDuration" : 4.4673908948898315,
"frequency" : 0,
"audioLevel" : 0.0012989747,
"amplitudeHistory" : [
0.366044,
0.35983628,
0.366044,
0.3723677,
0.3723677,
0.38537663,
0.3988907,
0.37881085,
1.1222931,
0.97146225
],
"timeSinceLastOscillation" : 0.49455296993255615,
"isBreathing" : true,
"pressure" : 0,
"timeSinceBreathStart" : 4.4673908948898315,
"timestamp" : "2025-07-19T12:02:26Z",
"breathCount" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [AUDIO] Audio processing pipeline
DATA: {
"filteredSamples" : 4800,
"processingStepTimeMs" : {
"smoothing" : 0.12195110321044922,
"downsampling" : 1.2749433517456055,
"autocorrelation" : 0.1919269561767578,
"filtering" : 237.64896392822266,
"squaring" : 0.012993812561035156
},
"maxAudioLevel" : 0.001511965,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:26Z",
"minAudioLevel" : 1.4691068e-08,
"averageAudioLevel" : 0.0003387236,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"audioLevel" : 0.001511965,
"calculatedPressure" : 0,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "completed",
"hasOscillation" : false,
"breathDuration" : 0,
"frequency" : 0,
"audioLevel" : 0.001511965,
"amplitudeHistory" : [

],
"timeSinceLastOscillation" : 0.7403069734573364,
"isBreathing" : false,
"pressure" : 0,
"timeSinceBreathStart" : 4.713144898414612,
"timestamp" : "2025-07-19T12:02:26Z",
"breathCount" : 1
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0012115816,
"smoothedSamples" : 97,
"timestamp" : "2025-07-19T12:02:26Z",
"downsampledSamples" : 97,
"inputSamples" : 4800,
"minAudioLevel" : 9.8105374e-08,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"averageAudioLevel" : 0.000330126,
"processingStepTimeMs" : {
"smoothing" : 0.12195110321044922,
"filtering" : 237.11001873016357,
"squaring" : 0.012993812561035156,
"autocorrelation" : 0.19097328186035156,
"downsampling" : 1.2669563293457031
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"audioLevel" : 0.0012115816,
"detectedPitch" : 0,
"calculatedPressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:26Z] [BREATH] Breath detection analysis
DATA: {
"amplitudeHistory" : [

],
"frequency" : 0,
"isBreathing" : false,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:26Z",
"audioLevel" : 0.0012115816,
"currentState" : "idle",
"breathCount" : 1,
"hasOscillation" : false,
"pressure" : 0,
"breathDuration" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 1.9059644e-07,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"filtering" : 236.907958984375,
"smoothing" : 0.12600421905517578,
"squaring" : 0.02300739288330078,
"downsampling" : 1.2940168380737305,
"autocorrelation" : 0.20301342010498047
},
"inputSamples" : 4800,
"downsampledSamples" : 97,
"filteredSamples" : 4800,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.0003273696,
"maxAudioLevel" : 0.0014904537,
"timestamp" : "2025-07-19T12:02:27Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 0,
"calculatedPressure" : 0,
"audioLevel" : 0.0014904537,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [BREATH] Breath detection analysis
DATA: {
"amplitudeHistory" : [

],
"frequency" : 0,
"isBreathing" : false,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:27Z",
"audioLevel" : 0.0014904537,
"currentState" : "idle",
"breathCount" : 1,
"hasOscillation" : false,
"pressure" : 0,
"breathDuration" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 1.3286899e-07,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"downsampling" : 1.280069351196289,
"filtering" : 236.74297332763672,
"smoothing" : 0.12195110321044922,
"autocorrelation" : 0.20301342010498047,
"squaring" : 0.011920928955078125
},
"inputSamples" : 4800,
"downsampledSamples" : 97,
"filteredSamples" : 4800,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.0003282054,
"maxAudioLevel" : 0.0014306412,
"timestamp" : "2025-07-19T12:02:27Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 0,
"audioLevel" : 0.0014306412,
"calculatedPressure" : 0,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"currentState" : "idle",
"hasOscillation" : false,
"audioLevel" : 0.0014306412,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:27Z",
"breathDuration" : 0,
"breathCount" : 1,
"amplitudeHistory" : [

],
"isBreathing" : false,
"pressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 1.14360546e-07,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"squaring" : 0.02300739288330078,
"filtering" : 238.1349802017212,
"autocorrelation" : 0.2009868621826172,
"downsampling" : 1.298069953918457,
"smoothing" : 0.12302398681640625
},
"inputSamples" : 4800,
"filteredSamples" : 4800,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.00037344114,
"maxAudioLevel" : 0.0014175212,
"timestamp" : "2025-07-19T12:02:27Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0014175212,
"frameCount" : 4800,
"detectedPitch" : 0,
"calculatedPressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"currentState" : "idle",
"hasOscillation" : false,
"audioLevel" : 0.0014175212,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:27Z",
"breathDuration" : 0,
"breathCount" : 1,
"amplitudeHistory" : [

],
"isBreathing" : false,
"pressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 4.041067e-08,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"autocorrelation" : 0.19097328186035156,
"downsampling" : 1.2630224227905273,
"filtering" : 236.87398433685303,
"squaring" : 0.02396106719970703,
"smoothing" : 0.12302398681640625
},
"inputSamples" : 4800,
"filteredSamples" : 4800,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.00035360837,
"maxAudioLevel" : 0.0013486808,
"timestamp" : "2025-07-19T12:02:27Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"frameCount" : 4800,
"audioLevel" : 0.0013486808,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:27Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"currentState" : "idle",
"hasOscillation" : false,
"audioLevel" : 0.0013486808,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:27Z",
"breathDuration" : 0,
"breathCount" : 1,
"amplitudeHistory" : [

],
"isBreathing" : false,
"pressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 4.726735e-08,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"autocorrelation" : 0.19800662994384766,
"filtering" : 237.05899715423584,
"downsampling" : 1.2879371643066406,
"smoothing" : 0.12302398681640625
},
"inputSamples" : 4800,
"filteredSamples" : 4800,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.00030000217,
"maxAudioLevel" : 0.001638886,
"timestamp" : "2025-07-19T12:02:28Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 0,
"audioLevel" : 0.001638886,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"currentState" : "idle",
"hasOscillation" : false,
"audioLevel" : 0.001638886,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:28Z",
"breathDuration" : 0,
"breathCount" : 1,
"amplitudeHistory" : [

],
"isBreathing" : false,
"pressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 1.9750041e-07,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"downsampling" : 1.2890100479125977,
"squaring" : 0.012040138244628906,
"filtering" : 236.7769479751587,
"smoothing" : 0.12099742889404297,
"autocorrelation" : 0.20003318786621094
},
"inputSamples" : 4800,
"downsampledSamples" : 97,
"filteredSamples" : 4800,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.0005825384,
"maxAudioLevel" : 0.0019368527,
"timestamp" : "2025-07-19T12:02:28Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"detectedPitch" : 0,
"frameCount" : 4800,
"audioLevel" : 0.0019368527
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"currentState" : "idle",
"hasOscillation" : false,
"audioLevel" : 0.0019368527,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:28Z",
"breathDuration" : 0,
"breathCount" : 1,
"amplitudeHistory" : [

],
"isBreathing" : false,
"pressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 1.4561147e-07,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"downsampling" : 1.2819766998291016,
"squaring" : 0.012040138244628906,
"filtering" : 237.32101917266846,
"smoothing" : 0.1220703125,
"autocorrelation" : 0.2009868621826172
},
"inputSamples" : 4800,
"downsampledSamples" : 97,
"filteredSamples" : 4800,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.00040524817,
"maxAudioLevel" : 0.0014958518,
"timestamp" : "2025-07-19T12:02:28Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"audioLevel" : 0.0014958518,
"detectedPitch" : 0,
"calculatedPressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"currentState" : "idle",
"hasOscillation" : false,
"audioLevel" : 0.0014958518,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:28Z",
"breathDuration" : 0,
"breathCount" : 1,
"amplitudeHistory" : [

],
"isBreathing" : false,
"pressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 6.4301275e-08,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"filtering" : 236.8389368057251,
"downsampling" : 1.2890100479125977,
"autocorrelation" : 0.22304058074951172,
"smoothing" : 0.12195110321044922
},
"inputSamples" : 4800,
"filteredSamples" : 4800,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.00045795506,
"maxAudioLevel" : 0.0017999992,
"timestamp" : "2025-07-19T12:02:28Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0017999992,
"detectedPitch" : 0,
"calculatedPressure" : 0,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:28Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"currentState" : "idle",
"hasOscillation" : false,
"audioLevel" : 0.0017999992,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:28Z",
"breathDuration" : 0,
"breathCount" : 1,
"amplitudeHistory" : [

],
"isBreathing" : false,
"pressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 3.2928074e-07,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"smoothing" : 0.12302398681640625,
"filtering" : 236.8769645690918,
"autocorrelation" : 0.20396709442138672,
"squaring" : 0.012040138244628906,
"downsampling" : 1.291036605834961
},
"inputSamples" : 4800,
"filteredSamples" : 4800,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.0023262973,
"maxAudioLevel" : 0.017856957,
"timestamp" : "2025-07-19T12:02:29Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.017856957,
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"currentState" : "starting",
"hasOscillation" : true,
"audioLevel" : 0.017856957,
"timeSinceLastOscillation" : 0,
"timestamp" : "2025-07-19T12:02:29Z",
"stateTransitionReason" : "Oscillation detected, starting new breath",
"breathDuration" : 0,
"breathCount" : 1,
"amplitudeHistory" : [
0.005
],
"isBreathing" : true,
"pressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 2.9492633e-09,
"processingTimeMs" : 1.9310712814331055,
"audioLevel" : 0.00053287507,
"targetMinFreq" : 15.971606,
"economicalSearchUsed" : true,
"downsampledSamples" : 300,
"targetMaxFreq" : 26.619343,
"runLength" : 0,
"correlation" : 0.7768559,
"movingAveAmplitude" : 0,
"audioSamples" : 300,
"detectedPitch" : 21.295475,
"bestPeriod" : 46,
"searchStrategy" : "economical",
"timestamp" : "2025-07-19T12:02:29Z",
"movingAvePeriod" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 4.204223e-06,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"filtering" : 236.83202266693115,
"downsampling" : 1.2760162353515625,
"squaring" : 0.024080276489257812,
"autocorrelation" : 2.042055130004883,
"smoothing" : 0.12600421905517578
},
"inputSamples" : 4800,
"downsampledSamples" : 97,
"filteredSamples" : 4800,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.006701013,
"maxAudioLevel" : 0.042144526,
"timestamp" : "2025-07-19T12:02:29Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"audioLevel" : 0.042144526,
"detectedPitch" : 21.295475,
"calculatedPressure" : 19.170635
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 0.24693691730499268,
"frequency" : 21.295475,
"audioLevel" : 0.042144526,
"timeSinceBreathStart" : 0.24693691730499268,
"timeSinceLastOscillation" : 0,
"amplitudeHistory" : [
0.005,
1.2780423
],
"isBreathing" : true,
"pressure" : 19.170635,
"timestamp" : "2025-07-19T12:02:29Z",
"breathCount" : 1
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 3.299362e-09,
"processingTimeMs" : 1.685023307800293,
"audioLevel" : 0.00053287507,
"targetMinFreq" : 16.697588,
"economicalSearchUsed" : true,
"downsampledSamples" : 300,
"targetMaxFreq" : 27.829313,
"runLength" : 1,
"correlation" : 0.61049664,
"movingAveAmplitude" : 0.61049664,
"audioSamples" : 300,
"timestamp" : "2025-07-19T12:02:29Z",
"detectedPitch" : 22.26345,
"searchStrategy" : "economical",
"bestPeriod" : 44,
"movingAvePeriod" : 44
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"downsampling" : 1.2840032577514648,
"squaring" : 0.012040138244628906,
"smoothing" : 0.1220703125,
"autocorrelation" : 1.7870664596557617,
"filtering" : 236.9060516357422
},
"maxAudioLevel" : 0.035041194,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:29Z",
"minAudioLevel" : 1.4353645e-07,
"averageAudioLevel" : 0.005716433,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 20.2538,
"frameCount" : 4800,
"detectedPitch" : 22.26345,
"audioLevel" : 0.035041194
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 0.4937279224395752,
"audioLevel" : 0.035041194,
"timeSinceBreathStart" : 0.4937279224395752,
"amplitudeHistory" : [
0.005,
1.2780423,
1.3502533
],
"timeSinceLastOscillation" : 0,
"pressure" : 20.2538,
"isBreathing" : true,
"frequency" : 22.26345,
"timestamp" : "2025-07-19T12:02:29Z",
"breathCount" : 1
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 4.2799915e-09,
"processingTimeMs" : 1.4929771423339844,
"audioLevel" : 0.00053287507,
"targetMinFreq" : 16.697588,
"economicalSearchUsed" : true,
"downsampledSamples" : 300,
"targetMaxFreq" : 27.829313,
"runLength" : 2,
"correlation" : 0.6782161,
"movingAveAmplitude" : 0.64435637,
"audioSamples" : 300,
"detectedPitch" : 22.26345,
"bestPeriod" : 44,
"searchStrategy" : "economical",
"timestamp" : "2025-07-19T12:02:29Z",
"movingAvePeriod" : 44
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 4.757545e-06,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"squaring" : 0.02396106719970703,
"autocorrelation" : 1.5970468521118164,
"smoothing" : 0.1220703125,
"downsampling" : 1.3049840927124023,
"filtering" : 236.9149923324585
},
"inputSamples" : 4800,
"filteredSamples" : 4800,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.00661101,
"maxAudioLevel" : 0.045334432,
"timestamp" : "2025-07-19T12:02:29Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.045334432,
"calculatedPressure" : 20.2538,
"frameCount" : 4800,
"detectedPitch" : 22.26345
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:29Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 0.7403559684753418,
"frequency" : 22.26345,
"audioLevel" : 0.045334432,
"timeSinceBreathStart" : 0.7403559684753418,
"timeSinceLastOscillation" : 0,
"amplitudeHistory" : [
0.005,
1.2780423,
1.3502533,
1.3502533
],
"isBreathing" : true,
"pressure" : 20.2538,
"timestamp" : "2025-07-19T12:02:29Z",
"breathCount" : 1
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 4.839452e-09,
"processingTimeMs" : 1.5130043029785156,
"audioLevel" : 0.00053287507,
"targetMinFreq" : 17.919363,
"economicalSearchUsed" : true,
"downsampledSamples" : 300,
"targetMaxFreq" : 29.865606,
"runLength" : 3,
"correlation" : 0.6868853,
"movingAveAmplitude" : 0.6585327,
"audioSamples" : 300,
"timestamp" : "2025-07-19T12:02:30Z",
"detectedPitch" : 23.892485,
"searchStrategy" : "economical",
"bestPeriod" : 41,
"movingAvePeriod" : 43
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"filtering" : 236.89699172973633,
"smoothing" : 0.12302398681640625,
"squaring" : 0.012993812561035156,
"downsampling" : 1.2650489807128906,
"autocorrelation" : 1.6199350357055664
},
"maxAudioLevel" : 0.044024155,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:30Z",
"minAudioLevel" : 2.2736494e-06,
"averageAudioLevel" : 0.0064662932,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 22.076689,
"audioLevel" : 0.044024155,
"detectedPitch" : 23.892485,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 0.9869199991226196,
"audioLevel" : 0.044024155,
"timeSinceBreathStart" : 0.9869199991226196,
"amplitudeHistory" : [
0.005,
1.2780423,
1.3502533,
1.3502533,
1.4717792
],
"timeSinceLastOscillation" : 0,
"pressure" : 22.076689,
"isBreathing" : true,
"frequency" : 23.892485,
"breathCount" : 1,
"timestamp" : "2025-07-19T12:02:30Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 5.061193e-09,
"processingTimeMs" : 1.5330314636230469,
"audioLevel" : 0.0003815446,
"targetMinFreq" : 17.919363,
"economicalSearchUsed" : true,
"downsampledSamples" : 300,
"targetMaxFreq" : 29.865606,
"runLength" : 4,
"correlation" : 0.7387032,
"movingAveAmplitude" : 0.67857534,
"audioSamples" : 300,
"detectedPitch" : 23.892485,
"bestPeriod" : 41,
"searchStrategy" : "economical",
"timestamp" : "2025-07-19T12:02:30Z",
"movingAvePeriod" : 42.5
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 1.1253869e-06,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"filtering" : 237.3208999633789,
"downsampling" : 1.2850761413574219,
"smoothing" : 0.12600421905517578,
"squaring" : 0.025033950805664062,
"autocorrelation" : 1.649022102355957
},
"inputSamples" : 4800,
"filteredSamples" : 4800,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.007220006,
"maxAudioLevel" : 0.048913036,
"timestamp" : "2025-07-19T12:02:30Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.048913036,
"frameCount" : 4800,
"calculatedPressure" : 22.076689,
"detectedPitch" : 23.892485
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 1.2339959144592285,
"frequency" : 23.892485,
"audioLevel" : 0.048913036,
"timeSinceBreathStart" : 1.2339959144592285,
"timeSinceLastOscillation" : 0,
"amplitudeHistory" : [
0.005,
1.2780423,
1.3502533,
1.3502533,
1.4717792,
1.4717792
],
"isBreathing" : true,
"pressure" : 22.076689,
"timestamp" : "2025-07-19T12:02:30Z",
"breathCount" : 1
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 6.5138384e-09,
"processingTimeMs" : 1.5469789505004883,
"audioLevel" : 0.0004272599,
"targetMinFreq" : 17.919363,
"economicalSearchUsed" : true,
"downsampledSamples" : 300,
"targetMaxFreq" : 29.865606,
"runLength" : 5,
"correlation" : 0.640147,
"movingAveAmplitude" : 0.6708897,
"audioSamples" : 300,
"detectedPitch" : 23.892485,
"bestPeriod" : 41,
"searchStrategy" : "economical",
"timestamp" : "2025-07-19T12:02:30Z",
"movingAvePeriod" : 42.2
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [AUDIO] Audio processing pipeline
DATA: {
"minAudioLevel" : 3.7171412e-06,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"smoothing" : 0.12302398681640625,
"downsampling" : 1.3030767440795898,
"autocorrelation" : 1.6520023345947266,
"filtering" : 237.14900016784668,
"squaring" : 0.011920928955078125
},
"inputSamples" : 4800,
"downsampledSamples" : 97,
"filteredSamples" : 4800,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.007548962,
"maxAudioLevel" : 0.049077913,
"timestamp" : "2025-07-19T12:02:30Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.049077913,
"frameCount" : 4800,
"calculatedPressure" : 22.076689,
"detectedPitch" : 23.892485
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"hasOscillation" : true,
"breathDuration" : 1.480915904045105,
"audioLevel" : 0.049077913,
"timeSinceBreathStart" : 1.480915904045105,
"amplitudeHistory" : [
0.005,
1.2780423,
1.3502533,
1.3502533,
1.4717792,
1.4717792,
1.4717792
],
"pressure" : 22.076689,
"isBreathing" : true,
"frequency" : 23.892485,
"timeSinceLastOscillation" : 0,
"breathCount" : 1,
"timestamp" : "2025-07-19T12:02:30Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 9.682712e-09,
"processingTimeMs" : 1.544952392578125,
"audioLevel" : 0.00047929218,
"targetMinFreq" : 15.971606,
"economicalSearchUsed" : true,
"downsampledSamples" : 300,
"targetMaxFreq" : 26.619343,
"runLength" : 5,
"correlation" : 0.6150067,
"movingAveAmplitude" : 0.6597131,
"audioSamples" : 300,
"detectedPitch" : 21.295475,
"timestamp" : "2025-07-19T12:02:30Z",
"searchStrategy" : "economical",
"bestPeriod" : 46,
"movingAvePeriod" : 42.960003
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.050470833,
"squaredSamples" : 4800,
"minAudioLevel" : 2.1548476e-07,
"processingStepTimeMs" : {
"autocorrelation" : 1.6459226608276367,
"squaring" : 0.012993812561035156,
"downsampling" : 1.2890100479125977,
"smoothing" : 0.12302398681640625,
"filtering" : 237.10095882415771
},
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:30Z",
"averageAudioLevel" : 0.0082318755,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"audioLevel" : 0.050470833,
"detectedPitch" : 21.295475,
"calculatedPressure" : 19.170635
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:30Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"currentState" : "active",
"hasOscillation" : true,
"breathDuration" : 1.7277148962020874,
"audioLevel" : 0.050470833,
"timeSinceBreathStart" : 1.7277148962020874,
"amplitudeHistory" : [
0.005,
1.2780423,
1.3502533,
1.3502533,
1.4717792,
1.4717792,
1.4717792,
1.2780423
],
"pressure" : 19.170635,
"isBreathing" : true,
"timeSinceLastOscillation" : 0,
"frequency" : 21.295475,
"breathCount" : 1,
"timestamp" : "2025-07-19T12:02:30Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 1.1119474e-08,
"processingTimeMs" : 4.871010780334473,
"audioLevel" : 0.00064092706,
"targetMinFreq" : 7,
"economicalSearchUsed" : false,
"downsampledSamples" : 300,
"targetMaxFreq" : 9.874918,
"runLength" : 3,
"correlation" : 0.7406,
"movingAveAmplitude" : 0.68667537,
"audioSamples" : 300,
"detectedPitch" : 7.8999343,
"timestamp" : "2025-07-19T12:02:31Z",
"searchStrategy" : "fallback",
"bestPeriod" : 124,
"movingAvePeriod" : 69.97334
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.056469336,
"squaredSamples" : 4800,
"minAudioLevel" : 4.396774e-06,
"processingStepTimeMs" : {
"smoothing" : 0.1239776611328125,
"squaring" : 0.024080276489257812,
"filtering" : 236.35292053222656,
"downsampling" : 1.2850761413574219,
"autocorrelation" : 4.9839019775390625
},
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:31Z",
"averageAudioLevel" : 0.0074912747,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 4.181026,
"frameCount" : 4800,
"audioLevel" : 0.056469336,
"detectedPitch" : 7.8999343
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"currentState" : "active",
"hasOscillation" : true,
"breathDuration" : 1.9772069454193115,
"frequency" : 7.8999343,
"timeSinceBreathStart" : 1.9772069454193115,
"pressure" : 4.181026,
"timeSinceLastOscillation" : 0,
"isBreathing" : true,
"amplitudeHistory" : [
0.005,
1.2780423,
1.3502533,
1.3502533,
1.4717792,
1.4717792,
1.4717792,
1.2780423,
0.27873507
],
"audioLevel" : 0.056469336,
"timestamp" : "2025-07-19T12:02:31Z",
"breathCount" : 1
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 1.1192016e-08,
"processingTimeMs" : 2.4080276489257812,
"audioLevel" : 0.00064092706,
"targetMinFreq" : 7,
"economicalSearchUsed" : true,
"downsampledSamples" : 300,
"targetMaxFreq" : 9.874918,
"runLength" : 2,
"correlation" : 0.704807,
"movingAveAmplitude" : 0.6957412,
"audioSamples" : 300,
"detectedPitch" : 7.8999343,
"timestamp" : "2025-07-19T12:02:31Z",
"searchStrategy" : "economical",
"bestPeriod" : 124,
"movingAvePeriod" : 96.98667
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.042568564,
"squaredSamples" : 4800,
"minAudioLevel" : 4.986068e-07,
"processingStepTimeMs" : {
"squaring" : 0.024080276489257812,
"filtering" : 236.80102825164795,
"autocorrelation" : 2.514958381652832,
"smoothing" : 0.12195110321044922,
"downsampling" : 1.280069351196289
},
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:31Z",
"averageAudioLevel" : 0.008479349,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.042568564,
"calculatedPressure" : 4.181026,
"frameCount" : 4800,
"detectedPitch" : 7.8999343
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"currentState" : "active",
"hasOscillation" : true,
"breathDuration" : 2.2246599197387695,
"frequency" : 7.8999343,
"pressure" : 4.181026,
"amplitudeHistory" : [
0.005,
1.2780423,
1.3502533,
1.3502533,
1.4717792,
1.4717792,
1.4717792,
1.2780423,
0.27873507,
0.27873507
],
"timeSinceLastOscillation" : 0,
"timeSinceBreathStart" : 2.2246599197387695,
"isBreathing" : true,
"audioLevel" : 0.042568564,
"breathCount" : 1,
"timestamp" : "2025-07-19T12:02:31Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [PITCH] Pitch detection analysis
DATA: {
"movingAveAmplitude" : 0.7077337,
"searchStrategy" : "economical",
"targetMinFreq" : 7,
"processingTimeMs" : 2.4570226669311523,
"runLength" : 3,
"targetMaxFreq" : 10.28983,
"correlation" : 0.73171866,
"bestPeriod" : 119,
"audioLevel" : 0.00064092706,
"audioVariance" : 1.1560102e-08,
"audioSamples" : 300,
"timestamp" : "2025-07-19T12:02:31Z",
"movingAvePeriod" : 104.32445,
"downsampledSamples" : 300,
"detectedPitch" : 8.231864,
"economicalSearchUsed" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.05418855,
"squaredSamples" : 4800,
"minAudioLevel" : 1.4719553e-06,
"processingStepTimeMs" : {
"autocorrelation" : 2.555966377258301,
"squaring" : 0.012993812561035156,
"filtering" : 237.35690116882324,
"downsampling" : 1.2880563735961914,
"smoothing" : 0.1220703125
},
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:31Z",
"averageAudioLevel" : 0.008525283,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"detectedPitch" : 8.231864,
"calculatedPressure" : 4.5524554,
"audioLevel" : 0.05418855
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [BREATH] Breath detection analysis
DATA: {
"timeSinceBreathStart" : 2.472706913948059,
"frequency" : 8.231864,
"hasOscillation" : true,
"timestamp" : "2025-07-19T12:02:31Z",
"breathDuration" : 2.472706913948059,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"isBreathing" : true,
"breathCount" : 1,
"currentState" : "active",
"pressure" : 4.5524554,
"amplitudeHistory" : [
1.2780423,
1.3502533,
1.3502533,
1.4717792,
1.4717792,
1.4717792,
1.2780423,
0.27873507,
0.27873507,
0.30349702
],
"timeSinceLastOscillation" : 0,
"audioLevel" : 0.05418855
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [PITCH] Pitch detection analysis
DATA: {
"detectedPitch" : 22.26345,
"targetMinFreq" : 16.697588,
"economicalSearchUsed" : false,
"bestPeriod" : 44,
"timestamp" : "2025-07-19T12:02:31Z",
"targetMaxFreq" : 27.829313,
"audioSamples" : 300,
"movingAveAmplitude" : 0,
"movingAvePeriod" : 0,
"runLength" : 0,
"searchStrategy" : "fallback",
"audioVariance" : 9.846662e-09,
"processingTimeMs" : 6.247997283935547,
"downsampledSamples" : 300,
"audioLevel" : 0.0005165189,
"correlation" : 0.6746102
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.041890327,
"squaredSamples" : 4800,
"minAudioLevel" : 2.7475762e-06,
"processingStepTimeMs" : {
"autocorrelation" : 6.365060806274414,
"squaring" : 0.011920928955078125,
"smoothing" : 0.12195110321044922,
"filtering" : 236.53602600097656,
"downsampling" : 1.2879371643066406
},
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:31Z",
"averageAudioLevel" : 0.0069794534,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 20.2538,
"audioLevel" : 0.041890327,
"detectedPitch" : 22.26345
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:31Z] [BREATH] Breath detection analysis
DATA: {
"breathCount" : 1,
"amplitudeHistory" : [
1.3502533,
1.3502533,
1.4717792,
1.4717792,
1.4717792,
1.2780423,
0.27873507,
0.27873507,
0.30349702,
1.3502533
],
"currentState" : "active",
"timeSinceLastOscillation" : 0,
"hasOscillation" : true,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"pressure" : 20.2538,
"frequency" : 22.26345,
"breathDuration" : 2.723713994026184,
"timestamp" : "2025-07-19T12:02:31Z",
"timeSinceBreathStart" : 2.723713994026184,
"isBreathing" : true,
"audioLevel" : 0.041890327
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [PITCH] Pitch detection analysis
DATA: {
"economicalSearchUsed" : true,
"processingTimeMs" : 1.5270709991455078,
"movingAveAmplitude" : 0.73446065,
"runLength" : 1,
"audioSamples" : 300,
"detectedPitch" : 22.26345,
"downsampledSamples" : 300,
"movingAvePeriod" : 44,
"timestamp" : "2025-07-19T12:02:32Z",
"targetMinFreq" : 16.697588,
"targetMaxFreq" : 27.829313,
"audioVariance" : 9.536926e-09,
"searchStrategy" : "economical",
"bestPeriod" : 44,
"correlation" : 0.73446065,
"audioLevel" : 0.0005165189
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.060606558,
"squaredSamples" : 4800,
"minAudioLevel" : 2.304383e-06,
"processingStepTimeMs" : {
"filtering" : 236.88197135925293,
"downsampling" : 1.289963722229004,
"squaring" : 0.02396106719970703,
"autocorrelation" : 1.6399621963500977,
"smoothing" : 0.12290477752685547
},
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:32Z",
"averageAudioLevel" : 0.0075802817,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.060606558,
"frameCount" : 4800,
"detectedPitch" : 22.26345,
"calculatedPressure" : 20.2538
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : true,
"timeSinceLastOscillation" : 0,
"frequency" : 22.26345,
"audioLevel" : 0.060606558,
"hasOscillation" : true,
"amplitudeHistory" : [
1.3502533,
1.4717792,
1.4717792,
1.4717792,
1.2780423,
0.27873507,
0.27873507,
0.30349702,
1.3502533,
1.3502533
],
"timestamp" : "2025-07-19T12:02:32Z",
"pressure" : 20.2538,
"currentState" : "active",
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"breathDuration" : 2.971916913986206,
"timeSinceBreathStart" : 2.971916913986206,
"breathCount" : 1
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [PITCH] Pitch detection analysis
DATA: {
"audioLevel" : 0.0008676971,
"detectedPitch" : 20.84238,
"processingTimeMs" : 1.5530586242675781,
"movingAveAmplitude" : 0.68938506,
"bestPeriod" : 47,
"runLength" : 2,
"timestamp" : "2025-07-19T12:02:32Z",
"economicalSearchUsed" : true,
"downsampledSamples" : 300,
"correlation" : 0.64430946,
"searchStrategy" : "economical",
"targetMaxFreq" : 26.052975,
"targetMinFreq" : 15.631785,
"audioSamples" : 300,
"movingAvePeriod" : 45.5,
"audioVariance" : 1.2164391e-08
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.054189928,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:32Z",
"downsampledSamples" : 97,
"minAudioLevel" : 7.160241e-06,
"averageAudioLevel" : 0.008129181,
"processingStepTimeMs" : {
"filtering" : 236.80710792541504,
"autocorrelation" : 1.6551017761230469,
"squaring" : 0.025033950805664062,
"smoothing" : 0.1220703125,
"downsampling" : 1.2879371643066406
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"detectedPitch" : 20.84238,
"calculatedPressure" : 18.663622,
"audioLevel" : 0.054189928
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [BREATH] Breath detection analysis
DATA: {
"timeSinceBreathStart" : 3.216936945915222,
"frequency" : 20.84238,
"timestamp" : "2025-07-19T12:02:32Z",
"hasOscillation" : true,
"breathDuration" : 3.216936945915222,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"isBreathing" : true,
"breathCount" : 1,
"pressure" : 18.663622,
"currentState" : "active",
"amplitudeHistory" : [
1.4717792,
1.4717792,
1.4717792,
1.2780423,
0.27873507,
0.27873507,
0.30349702,
1.3502533,
1.3502533,
1.2442415
],
"timeSinceLastOscillation" : 0,
"audioLevel" : 0.054189928
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [PITCH] Pitch detection analysis
DATA: {
"targetMinFreq" : 18.367348,
"detectedPitch" : 24.489796,
"economicalSearchUsed" : true,
"bestPeriod" : 40,
"timestamp" : "2025-07-19T12:02:32Z",
"targetMaxFreq" : 30.612244,
"audioSamples" : 300,
"movingAveAmplitude" : 0.67415076,
"movingAvePeriod" : 43.666668,
"runLength" : 3,
"searchStrategy" : "economical",
"audioVariance" : 1.5633226e-08,
"processingTimeMs" : 1.6510486602783203,
"downsampledSamples" : 300,
"audioLevel" : 0.0008676971,
"correlation" : 0.6436822
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.056144167,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:32Z",
"downsampledSamples" : 97,
"minAudioLevel" : 2.919929e-06,
"averageAudioLevel" : 0.008369546,
"processingStepTimeMs" : {
"filtering" : 236.80591583251953,
"downsampling" : 1.2710094451904297,
"autocorrelation" : 1.757979393005371,
"squaring" : 0.012993812561035156,
"smoothing" : 0.1239776611328125
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.056144167,
"frameCount" : 4800,
"detectedPitch" : 24.489796,
"calculatedPressure" : 22.745079
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [BREATH] Breath detection analysis
DATA: {
"currentState" : "active",
"isBreathing" : true,
"timeSinceLastOscillation" : 0,
"timeSinceBreathStart" : 3.463594913482666,
"audioLevel" : 0.056144167,
"breathCount" : 1,
"breathDuration" : 3.463594913482666,
"frequency" : 24.489796,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"hasOscillation" : true,
"pressure" : 22.745079,
"amplitudeHistory" : [
1.4717792,
1.4717792,
1.2780423,
0.27873507,
0.27873507,
0.30349702,
1.3502533,
1.3502533,
1.2442415,
1.5163386
],
"timestamp" : "2025-07-19T12:02:32Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 1.5762804e-08,
"processingTimeMs" : 1.5228986740112305,
"audioLevel" : 0.0008676971,
"targetMinFreq" : 18.367348,
"economicalSearchUsed" : true,
"downsampledSamples" : 300,
"targetMaxFreq" : 30.612244,
"runLength" : 4,
"correlation" : 0.6396225,
"movingAveAmplitude" : 0.6655187,
"audioSamples" : 300,
"detectedPitch" : 24.489796,
"bestPeriod" : 40,
"searchStrategy" : "economical",
"timestamp" : "2025-07-19T12:02:32Z",
"movingAvePeriod" : 42.75
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"averageAudioLevel" : 0.0069976407,
"maxAudioLevel" : 0.03892339,
"processingStepTimeMs" : {
"smoothing" : 0.12195110321044922,
"squaring" : 0.02300739288330078,
"filtering" : 236.9980812072754,
"downsampling" : 1.2829303741455078,
"autocorrelation" : 1.623988151550293
},
"squaredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:32Z",
"downsampledSamples" : 97,
"minAudioLevel" : 8.4238127e-07,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 22.745079,
"frameCount" : 4800,
"audioLevel" : 0.03892339,
"detectedPitch" : 24.489796
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:32Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"breathDuration" : 3.7103430032730103,
"timeSinceBreathStart" : 3.7103430032730103,
"currentState" : "active",
"breathCount" : 1,
"audioLevel" : 0.03892339,
"timeSinceLastOscillation" : 0,
"pressure" : 22.745079,
"timestamp" : "2025-07-19T12:02:32Z",
"amplitudeHistory" : [
1.4717792,
1.2780423,
0.27873507,
0.27873507,
0.30349702,
1.3502533,
1.3502533,
1.2442415,
1.5163386,
1.5163386
],
"frequency" : 24.489796,
"hasOscillation" : true,
"isBreathing" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [PITCH] Pitch detection analysis
DATA: {
"downsampledSamples" : 300,
"detectedPitch" : 23.892485,
"bestPeriod" : 41,
"correlation" : 0.6319827,
"audioSamples" : 300,
"audioVariance" : 1.5434063e-08,
"economicalSearchUsed" : true,
"movingAvePeriod" : 42.4,
"processingTimeMs" : 1.516103744506836,
"targetMaxFreq" : 29.865606,
"audioLevel" : 0.0007552418,
"movingAveAmplitude" : 0.6588115,
"timestamp" : "2025-07-19T12:02:33Z",
"targetMinFreq" : 17.919363,
"searchStrategy" : "economical",
"runLength" : 5
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.045099065,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:33Z",
"downsampledSamples" : 97,
"minAudioLevel" : 7.0749957e-07,
"averageAudioLevel" : 0.008325645,
"processingStepTimeMs" : {
"filtering" : 237.2039556503296,
"smoothing" : 0.1310110092163086,
"downsampling" : 1.2710094451904297,
"autocorrelation" : 1.6210079193115234,
"squaring" : 0.025033950805664062
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 22.076689,
"audioLevel" : 0.045099065,
"detectedPitch" : 23.892485,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : true,
"amplitudeHistory" : [
1.2780423,
0.27873507,
0.27873507,
0.30349702,
1.3502533,
1.3502533,
1.2442415,
1.5163386,
1.5163386,
1.4717792
],
"breathCount" : 1,
"audioLevel" : 0.045099065,
"frequency" : 23.892485,
"currentState" : "active",
"timeSinceBreathStart" : 3.9572649002075195,
"timestamp" : "2025-07-19T12:02:33Z",
"hasOscillation" : true,
"breathDuration" : 3.9572649002075195,
"timeSinceLastOscillation" : 0,
"pressure" : 22.076689,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 1.540064811706543,
"economicalSearchUsed" : true,
"correlation" : 0.70096415,
"audioLevel" : 0.0006857613,
"downsampledSamples" : 300,
"detectedPitch" : 20.408163,
"timestamp" : "2025-07-19T12:02:33Z",
"targetMaxFreq" : 25.510204,
"searchStrategy" : "economical",
"movingAvePeriod" : 43.52,
"runLength" : 5,
"targetMinFreq" : 15.306122,
"bestPeriod" : 48,
"audioVariance" : 1.1380094e-08,
"audioSamples" : 300,
"movingAveAmplitude" : 0.66724205
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"averageAudioLevel" : 0.008471208,
"maxAudioLevel" : 0.054881748,
"processingStepTimeMs" : {
"squaring" : 0.024080276489257812,
"filtering" : 236.50193214416504,
"autocorrelation" : 1.6390085220336914,
"smoothing" : 0.12993812561035156,
"downsampling" : 1.278996467590332
},
"squaredSamples" : 4800,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:33Z",
"minAudioLevel" : 1.1983793e-06,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.054881748,
"frameCount" : 4800,
"calculatedPressure" : 18.177732,
"detectedPitch" : 20.408163
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"timeSinceBreathStart" : 4.203483939170837,
"amplitudeHistory" : [
0.27873507,
0.27873507,
0.30349702,
1.3502533,
1.3502533,
1.2442415,
1.5163386,
1.5163386,
1.4717792,
1.2118489
],
"pressure" : 18.177732,
"currentState" : "active",
"timestamp" : "2025-07-19T12:02:33Z",
"audioLevel" : 0.054881748,
"hasOscillation" : true,
"breathDuration" : 4.203483939170837,
"breathCount" : 1,
"frequency" : 20.408163,
"isBreathing" : true,
"timeSinceLastOscillation" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [PITCH] Pitch detection analysis
DATA: {
"bestPeriod" : 48,
"targetMaxFreq" : 25.510204,
"targetMinFreq" : 15.306122,
"searchStrategy" : "economical",
"timestamp" : "2025-07-19T12:02:33Z",
"correlation" : 0.7048913,
"audioVariance" : 1.2160711e-08,
"audioLevel" : 0.0006857613,
"processingTimeMs" : 1.6570091247558594,
"economicalSearchUsed" : true,
"movingAvePeriod" : 44.416,
"movingAveAmplitude" : 0.6747719,
"audioSamples" : 300,
"detectedPitch" : 20.408163,
"downsampledSamples" : 300,
"runLength" : 5
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.05096643,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:33Z",
"downsampledSamples" : 97,
"minAudioLevel" : 5.3456824e-06,
"averageAudioLevel" : 0.006524094,
"processingStepTimeMs" : {
"autocorrelation" : 1.7559528350830078,
"squaring" : 0.012993812561035156,
"downsampling" : 1.2830495834350586,
"smoothing" : 0.12099742889404297,
"filtering" : 237.16390132904053
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 18.177732,
"audioLevel" : 0.05096643,
"detectedPitch" : 20.408163
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [BREATH] Breath detection analysis
DATA: {
"pressure" : 18.177732,
"hasOscillation" : true,
"timestamp" : "2025-07-19T12:02:33Z",
"audioLevel" : 0.05096643,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"isBreathing" : true,
"amplitudeHistory" : [
0.27873507,
0.30349702,
1.3502533,
1.3502533,
1.2442415,
1.5163386,
1.5163386,
1.4717792,
1.2118489,
1.2118489
],
"currentState" : "active",
"breathCount" : 1,
"frequency" : 20.408163,
"timeSinceBreathStart" : 4.450437903404236,
"breathDuration" : 4.450437903404236,
"timeSinceLastOscillation" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [PITCH] Pitch detection analysis
DATA: {
"detectedPitch" : 19.207684,
"targetMaxFreq" : 24.009605,
"correlation" : 0.6776621,
"bestPeriod" : 51,
"movingAvePeriod" : 45.732803,
"economicalSearchUsed" : true,
"timestamp" : "2025-07-19T12:02:33Z",
"processingTimeMs" : 1.649022102355957,
"targetMinFreq" : 14.405763,
"movingAveAmplitude" : 0.67534995,
"audioLevel" : 0.00042189672,
"audioSamples" : 300,
"downsampledSamples" : 300,
"audioVariance" : 6.191486e-09,
"runLength" : 5,
"searchStrategy" : "economical"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.017422345,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:33Z",
"downsampledSamples" : 97,
"minAudioLevel" : 3.193345e-07,
"averageAudioLevel" : 0.0033150539,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"autocorrelation" : 1.7490386962890625,
"downsampling" : 1.2699365615844727,
"smoothing" : 0.12195110321044922,
"filtering" : 237.89501190185547
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.017422345,
"calculatedPressure" : 16.834396,
"frameCount" : 4800,
"detectedPitch" : 19.207684
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:33Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : true,
"frequency" : 19.207684,
"currentState" : "active",
"audioLevel" : 0.017422345,
"timestamp" : "2025-07-19T12:02:33Z",
"breathCount" : 1,
"timeSinceBreathStart" : 4.698139905929565,
"breathDuration" : 4.698139905929565,
"hasOscillation" : true,
"pressure" : 16.834396,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"amplitudeHistory" : [
0.30349702,
1.3502533,
1.3502533,
1.2442415,
1.5163386,
1.5163386,
1.4717792,
1.2118489,
1.2118489,
1.1222931
],
"timeSinceLastOscillation" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [PITCH] Pitch detection analysis
DATA: {
"runLength" : 5,
"searchStrategy" : "economical",
"downsampledSamples" : 300,
"movingAveAmplitude" : 0.68300843,
"audioLevel" : 0.0003776421,
"timestamp" : "2025-07-19T12:02:34Z",
"bestPeriod" : 51,
"correlation" : 0.7136423,
"processingTimeMs" : 1.7729997634887695,
"movingAvePeriod" : 46.786243,
"audioVariance" : 3.1290692e-09,
"targetMaxFreq" : 24.009605,
"targetMinFreq" : 14.405763,
"audioSamples" : 300,
"detectedPitch" : 19.207684,
"economicalSearchUsed" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0020187586,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:34Z",
"downsampledSamples" : 97,
"minAudioLevel" : 8.227653e-08,
"averageAudioLevel" : 0.0005427145,
"processingStepTimeMs" : {
"smoothing" : 0.12195110321044922,
"downsampling" : 1.2649297714233398,
"filtering" : 236.48500442504883,
"squaring" : 0.012040138244628906,
"autocorrelation" : 1.8739700317382812
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0020187586,
"frameCount" : 4800,
"calculatedPressure" : 16.834396,
"detectedPitch" : 19.207684
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [BREATH] Breath detection analysis
DATA: {
"breathCount" : 1,
"amplitudeHistory" : [
1.3502533,
1.3502533,
1.2442415,
1.5163386,
1.5163386,
1.4717792,
1.2118489,
1.2118489,
1.1222931,
1.1222931
],
"currentState" : "active",
"timeSinceLastOscillation" : 0,
"hasOscillation" : true,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"timeSinceBreathStart" : 4.9445189237594604,
"isBreathing" : true,
"breathDuration" : 4.9445189237594604,
"timestamp" : "2025-07-19T12:02:34Z",
"pressure" : 16.834396,
"frequency" : 19.207684,
"audioLevel" : 0.0020187586
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [PITCH] Pitch detection analysis
DATA: {
"economicalSearchUsed" : true,
"processingTimeMs" : 1.7920732498168945,
"movingAveAmplitude" : 0.67938983,
"runLength" : 5,
"audioSamples" : 300,
"detectedPitch" : 19.99167,
"downsampledSamples" : 300,
"movingAvePeriod" : 47.228996,
"timestamp" : "2025-07-19T12:02:34Z",
"targetMinFreq" : 14.993753,
"targetMaxFreq" : 24.989588,
"audioLevel" : 7.7193305e-05,
"audioVariance" : 1.01026555e-10,
"bestPeriod" : 49,
"correlation" : 0.6649156,
"searchStrategy" : "economical"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [AUDIO] Audio processing pipeline
DATA: {
"downsampledSamples" : 97,
"inputSamples" : 4800,
"maxAudioLevel" : 0.0025753858,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.00066056394,
"filteredSamples" : 4800,
"minAudioLevel" : 1.1385782e-07,
"processingStepTimeMs" : {
"smoothing" : 0.1329183578491211,
"downsampling" : 1.2860298156738281,
"filtering" : 237.1809482574463,
"squaring" : 0.02300739288330078,
"autocorrelation" : 1.891016960144043
},
"smoothedSamples" : 97,
"timestamp" : "2025-07-19T12:02:34Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0025753858,
"frameCount" : 4800,
"calculatedPressure" : 17.711678,
"detectedPitch" : 19.99167
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [BREATH] Breath detection analysis
DATA: {
"audioLevel" : 0.0025753858,
"isBreathing" : true,
"timeSinceLastOscillation" : 0,
"timeSinceBreathStart" : 5.191690921783447,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"hasOscillation" : true,
"currentState" : "active",
"breathCount" : 1,
"breathDuration" : 5.191690921783447,
"amplitudeHistory" : [
1.3502533,
1.2442415,
1.5163386,
1.5163386,
1.4717792,
1.2118489,
1.2118489,
1.1222931,
1.1222931,
1.1807785
],
"frequency" : 19.99167,
"pressure" : 17.711678,
"timestamp" : "2025-07-19T12:02:34Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [AUDIO] Audio processing pipeline
DATA: {
"downsampledSamples" : 97,
"inputSamples" : 4800,
"maxAudioLevel" : 0.00190338,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.00052335847,
"filteredSamples" : 4800,
"minAudioLevel" : 8.018105e-09,
"processingStepTimeMs" : {
"autocorrelation" : 0.19109249114990234,
"squaring" : 0.02205371856689453,
"filtering" : 236.57190799713135,
"downsampling" : 1.2799501419067383,
"smoothing" : 0.13196468353271484
},
"smoothedSamples" : 97,
"timestamp" : "2025-07-19T12:02:34Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"detectedPitch" : 0,
"calculatedPressure" : 0,
"audioLevel" : 0.00190338
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [BREATH] Breath detection analysis
DATA: {
"audioLevel" : 0.00190338,
"isBreathing" : true,
"timeSinceLastOscillation" : 0.2448660135269165,
"timeSinceBreathStart" : 5.436556935310364,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"hasOscillation" : false,
"currentState" : "active",
"breathCount" : 1,
"breathDuration" : 5.436556935310364,
"amplitudeHistory" : [
1.3502533,
1.2442415,
1.5163386,
1.5163386,
1.4717792,
1.2118489,
1.2118489,
1.1222931,
1.1222931,
1.1807785
],
"frequency" : 0,
"pressure" : 0,
"timestamp" : "2025-07-19T12:02:34Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [AUDIO] Audio processing pipeline
DATA: {
"inputSamples" : 4800,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.0016673112,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.0005023163,
"filteredSamples" : 4800,
"minAudioLevel" : 1.0865642e-07,
"processingStepTimeMs" : {
"downsampling" : 1.2929439544677734,
"filtering" : 237.10298538208008,
"squaring" : 0.012993812561035156,
"autocorrelation" : 0.19097328186035156,
"smoothing" : 0.12195110321044922
},
"smoothedSamples" : 97,
"timestamp" : "2025-07-19T12:02:34Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0016673112,
"frameCount" : 4800,
"detectedPitch" : 0,
"calculatedPressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:34Z] [BREATH] Breath detection analysis
DATA: {
"audioLevel" : 0.0016673112,
"isBreathing" : true,
"timeSinceLastOscillation" : 0.49019908905029297,
"timeSinceBreathStart" : 5.68189001083374,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"hasOscillation" : false,
"currentState" : "ending",
"breathCount" : 1,
"breathDuration" : 5.68189001083374,
"amplitudeHistory" : [
1.3502533,
1.2442415,
1.5163386,
1.5163386,
1.4717792,
1.2118489,
1.2118489,
1.1222931,
1.1222931,
1.1807785
],
"frequency" : 0,
"pressure" : 0,
"timestamp" : "2025-07-19T12:02:34Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"averageAudioLevel" : 0.00035176368,
"maxAudioLevel" : 0.0012700375,
"processingStepTimeMs" : {
"autocorrelation" : 0.19109249114990234,
"squaring" : 0.012993812561035156,
"smoothing" : 0.12195110321044922,
"downsampling" : 1.2630224227905273,
"filtering" : 237.11204528808594
},
"squaredSamples" : 4800,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:35Z",
"minAudioLevel" : 3.574678e-08,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 0,
"audioLevel" : 0.0012700375,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"timeSinceBreathStart" : 5.927189946174622,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"isBreathing" : false,
"timestamp" : "2025-07-19T12:02:35Z",
"pressure" : 0,
"hasOscillation" : false,
"currentState" : "completed",
"amplitudeHistory" : [

],
"audioLevel" : 0.0012700375,
"breathDuration" : 0,
"breathCount" : 2,
"timeSinceLastOscillation" : 0.7354990243911743
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [AUDIO] Audio processing pipeline
DATA: {
"inputSamples" : 4800,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.0016877842,
"squaredSamples" : 4800,
"averageAudioLevel" : 0.00042573313,
"filteredSamples" : 4800,
"minAudioLevel" : 9.0757794e-08,
"processingStepTimeMs" : {
"downsampling" : 1.2860298156738281,
"autocorrelation" : 0.2149343490600586,
"smoothing" : 0.1220703125,
"squaring" : 0.02300739288330078,
"filtering" : 236.79602146148682
},
"smoothedSamples" : 97,
"timestamp" : "2025-07-19T12:02:35Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 0,
"calculatedPressure" : 0,
"frameCount" : 4800,
"audioLevel" : 0.0016877842
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : false,
"breathCount" : 2,
"amplitudeHistory" : [

],
"pressure" : 0,
"breathDuration" : 0,
"currentState" : "idle",
"frequency" : 0,
"audioLevel" : 0.0016877842,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:35Z",
"hasOscillation" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0021781474,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:35Z",
"downsampledSamples" : 97,
"minAudioLevel" : 2.566344e-08,
"averageAudioLevel" : 0.0004196832,
"processingStepTimeMs" : {
"downsampling" : 1.2799501419067383,
"squaring" : 0.013947486877441406,
"smoothing" : 0.12195110321044922,
"autocorrelation" : 0.19097328186035156,
"filtering" : 236.77992820739746
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.0021781474
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : false,
"breathCount" : 2,
"amplitudeHistory" : [

],
"pressure" : 0,
"breathDuration" : 0,
"audioLevel" : 0.0021781474,
"frequency" : 0,
"currentState" : "idle",
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:35Z",
"hasOscillation" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0015482858,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:35Z",
"downsampledSamples" : 97,
"minAudioLevel" : 8.9356035e-08,
"averageAudioLevel" : 0.00047980144,
"processingStepTimeMs" : {
"downsampling" : 1.2630224227905273,
"smoothing" : 0.12099742889404297,
"squaring" : 0.02396106719970703,
"filtering" : 237.29395866394043,
"autocorrelation" : 0.20992755889892578
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.0015482858,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:35Z] [BREATH] Breath detection analysis
DATA: {
"breathDuration" : 0,
"breathCount" : 2,
"isBreathing" : false,
"amplitudeHistory" : [

],
"audioLevel" : 0.0015482858,
"timestamp" : "2025-07-19T12:02:35Z",
"pressure" : 0,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "idle",
"hasOscillation" : false,
"frequency" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0019528046,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:36Z",
"downsampledSamples" : 97,
"minAudioLevel" : 2.9547664e-08,
"averageAudioLevel" : 0.00045566345,
"processingStepTimeMs" : {
"autocorrelation" : 0.2028942108154297,
"smoothing" : 0.12195110321044922,
"filtering" : 236.78994178771973,
"squaring" : 0.012993812561035156,
"downsampling" : 1.2649297714233398
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"frameCount" : 4800,
"audioLevel" : 0.0019528046,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [BREATH] Breath detection analysis
DATA: {
"breathDuration" : 0,
"breathCount" : 2,
"isBreathing" : false,
"amplitudeHistory" : [

],
"audioLevel" : 0.0019528046,
"pressure" : 0,
"timestamp" : "2025-07-19T12:02:36Z",
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "idle",
"hasOscillation" : false,
"frequency" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0014400296,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:36Z",
"downsampledSamples" : 97,
"minAudioLevel" : 8.61898e-08,
"averageAudioLevel" : 0.00040925984,
"processingStepTimeMs" : {
"smoothing" : 0.12600421905517578,
"autocorrelation" : 0.19800662994384766,
"squaring" : 0.02396106719970703,
"downsampling" : 1.2860298156738281,
"filtering" : 236.8619441986084
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"detectedPitch" : 0,
"audioLevel" : 0.0014400296,
"calculatedPressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [BREATH] Breath detection analysis
DATA: {
"breathDuration" : 0,
"breathCount" : 2,
"isBreathing" : false,
"amplitudeHistory" : [

],
"audioLevel" : 0.0014400296,
"pressure" : 0,
"timestamp" : "2025-07-19T12:02:36Z",
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "idle",
"frequency" : 0,
"hasOscillation" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0019413907,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:36Z",
"downsampledSamples" : 97,
"minAudioLevel" : 2.8530849e-08,
"averageAudioLevel" : 0.0004981582,
"processingStepTimeMs" : {
"downsampling" : 1.2749433517456055,
"smoothing" : 0.13506412506103516,
"autocorrelation" : 0.19693374633789062,
"squaring" : 0.012993812561035156,
"filtering" : 236.67609691619873
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0019413907,
"calculatedPressure" : 0,
"detectedPitch" : 0,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [BREATH] Breath detection analysis
DATA: {
"breathDuration" : 0,
"breathCount" : 2,
"isBreathing" : false,
"amplitudeHistory" : [

],
"audioLevel" : 0.0019413907,
"timestamp" : "2025-07-19T12:02:36Z",
"pressure" : 0,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "idle",
"hasOscillation" : false,
"frequency" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0016553211,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:36Z",
"downsampledSamples" : 97,
"minAudioLevel" : 6.360642e-08,
"averageAudioLevel" : 0.00045243587,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"downsampling" : 1.291036605834961,
"filtering" : 237.92505264282227,
"smoothing" : 0.12195110321044922,
"autocorrelation" : 0.20205974578857422
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.0016553211,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:36Z] [BREATH] Breath detection analysis
DATA: {
"breathDuration" : 0,
"breathCount" : 2,
"isBreathing" : false,
"amplitudeHistory" : [

],
"audioLevel" : 0.0016553211,
"timestamp" : "2025-07-19T12:02:36Z",
"pressure" : 0,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "idle",
"frequency" : 0,
"hasOscillation" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0024832496,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:37Z",
"downsampledSamples" : 97,
"minAudioLevel" : 2.5213376e-07,
"averageAudioLevel" : 0.0004259471,
"processingStepTimeMs" : {
"squaring" : 0.012040138244628906,
"autocorrelation" : 0.1920461654663086,
"filtering" : 236.7650270462036,
"downsampling" : 1.2860298156738281,
"smoothing" : 0.1220703125
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0024832496,
"frameCount" : 4800,
"detectedPitch" : 0,
"calculatedPressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [BREATH] Breath detection analysis
DATA: {
"breathDuration" : 0,
"breathCount" : 2,
"isBreathing" : false,
"amplitudeHistory" : [

],
"audioLevel" : 0.0024832496,
"timestamp" : "2025-07-19T12:02:37Z",
"pressure" : 0,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "idle",
"frequency" : 0,
"hasOscillation" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0015191344,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:37Z",
"downsampledSamples" : 97,
"minAudioLevel" : 2.7443434e-07,
"averageAudioLevel" : 0.00040769987,
"processingStepTimeMs" : {
"autocorrelation" : 0.19502639770507812,
"filtering" : 236.85598373413086,
"smoothing" : 0.12302398681640625,
"squaring" : 0.012040138244628906,
"downsampling" : 1.2960433959960938
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"detectedPitch" : 0,
"audioLevel" : 0.0015191344,
"calculatedPressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [BREATH] Breath detection analysis
DATA: {
"breathDuration" : 0,
"breathCount" : 2,
"isBreathing" : false,
"amplitudeHistory" : [

],
"audioLevel" : 0.0015191344,
"pressure" : 0,
"timestamp" : "2025-07-19T12:02:37Z",
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "idle",
"hasOscillation" : false,
"frequency" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 47.9832,
"correlation" : 0.71806455,
"timestamp" : "2025-07-19T12:02:37Z",
"runLength" : 5,
"searchStrategy" : "economical",
"targetMaxFreq" : 24.009605,
"audioSamples" : 300,
"targetMinFreq" : 14.405763,
"detectedPitch" : 19.207684,
"audioLevel" : 0.001846786,
"movingAveAmplitude" : 0.6871248,
"economicalSearchUsed" : true,
"bestPeriod" : 51,
"audioVariance" : 4.297791e-08,
"downsampledSamples" : 300,
"processingTimeMs" : 1.623988151550293
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.11243521,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:37Z",
"downsampledSamples" : 97,
"minAudioLevel" : 2.1327287e-06,
"averageAudioLevel" : 0.014036187,
"processingStepTimeMs" : {
"filtering" : 236.71495914459229,
"downsampling" : 1.2880563735961914,
"squaring" : 0.02300739288330078,
"autocorrelation" : 1.7319917678833008,
"smoothing" : 0.12302398681640625
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.11243521,
"calculatedPressure" : 16.834396,
"detectedPitch" : 19.207684,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [BREATH] Breath detection analysis
DATA: {
"breathDuration" : 0,
"breathCount" : 2,
"isBreathing" : true,
"amplitudeHistory" : [
0.005
],
"audioLevel" : 0.11243521,
"timestamp" : "2025-07-19T12:02:37Z",
"pressure" : 16.834396,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"currentState" : "starting",
"timeSinceLastOscillation" : 0,
"hasOscillation" : true,
"frequency" : 19.207684
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 48.98656,
"correlation" : 0.7215371,
"timestamp" : "2025-07-19T12:02:37Z",
"runLength" : 5,
"searchStrategy" : "economical",
"targetMaxFreq" : 23.10358,
"audioSamples" : 300,
"targetMinFreq" : 13.862148,
"detectedPitch" : 18.482864,
"audioLevel" : 0.0018765422,
"movingAveAmplitude" : 0.6940073,
"economicalSearchUsed" : true,
"bestPeriod" : 53,
"audioVariance" : 1.1293118e-07,
"downsampledSamples" : 300,
"processingTimeMs" : 1.8209218978881836
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.09764599,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:37Z",
"downsampledSamples" : 97,
"minAudioLevel" : 5.5758283e-06,
"averageAudioLevel" : 0.017966961,
"processingStepTimeMs" : {
"squaring" : 0.02396106719970703,
"downsampling" : 1.2819766998291016,
"filtering" : 237.7009391784668,
"smoothing" : 0.12099742889404297,
"autocorrelation" : 1.9249916076660156
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"audioLevel" : 0.09764599,
"detectedPitch" : 18.482864,
"calculatedPressure" : 16.023325
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 18.482864,
"timeSinceBreathStart" : 0.2477869987487793,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:37Z",
"pressure" : 16.023325,
"hasOscillation" : true,
"currentState" : "starting",
"amplitudeHistory" : [
0.005,
1.0682217
],
"audioLevel" : 0.09764599,
"breathDuration" : 0.2477869987487793,
"breathCount" : 2,
"timeSinceLastOscillation" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 49.98925,
"correlation" : 0.71022254,
"timestamp" : "2025-07-19T12:02:37Z",
"runLength" : 5,
"searchStrategy" : "economical",
"targetMaxFreq" : 22.67574,
"audioSamples" : 300,
"targetMinFreq" : 13.605443,
"detectedPitch" : 18.14059,
"audioLevel" : 0.0018765422,
"movingAveAmplitude" : 0.6972503,
"economicalSearchUsed" : true,
"bestPeriod" : 54,
"audioVariance" : 9.908993e-08,
"downsampledSamples" : 300,
"processingTimeMs" : 1.7560720443725586
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"averageAudioLevel" : 0.014142742,
"maxAudioLevel" : 0.07414403,
"processingStepTimeMs" : {
"squaring" : 0.012040138244628906,
"filtering" : 237.11097240447998,
"autocorrelation" : 1.8590688705444336,
"downsampling" : 1.291036605834961,
"smoothing" : 0.12195110321044922
},
"squaredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:37Z",
"downsampledSamples" : 97,
"minAudioLevel" : 1.3803365e-05,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.07414403,
"frameCount" : 4800,
"detectedPitch" : 18.14059,
"calculatedPressure" : 15.64032
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:37Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 18.14059,
"timeSinceBreathStart" : 0.4948199987411499,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:37Z",
"pressure" : 15.64032,
"hasOscillation" : true,
"currentState" : "starting",
"timeSinceLastOscillation" : 0,
"amplitudeHistory" : [
0.005,
1.0682217,
1.042688
],
"breathDuration" : 0.4948199987411499,
"breathCount" : 2,
"audioLevel" : 0.07414403
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 50.5914,
"correlation" : 0.7147561,
"timestamp" : "2025-07-19T12:02:38Z",
"runLength" : 5,
"searchStrategy" : "economical",
"targetMaxFreq" : 23.10358,
"audioSamples" : 300,
"targetMinFreq" : 13.862148,
"detectedPitch" : 18.482864,
"audioLevel" : 0.0018765422,
"movingAveAmplitude" : 0.7007514,
"economicalSearchUsed" : true,
"bestPeriod" : 53,
"audioVariance" : 9.861351e-08,
"downsampledSamples" : 300,
"processingTimeMs" : 1.782059669494629
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.07984167,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:38Z",
"downsampledSamples" : 97,
"minAudioLevel" : 4.0112063e-06,
"averageAudioLevel" : 0.014628679,
"processingStepTimeMs" : {
"squaring" : 0.011920928955078125,
"downsampling" : 1.2900829315185547,
"filtering" : 236.77098751068115,
"autocorrelation" : 1.8930435180664062,
"smoothing" : 0.12302398681640625
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.07984167,
"detectedPitch" : 18.482864,
"calculatedPressure" : 16.023325,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 18.482864,
"timeSinceBreathStart" : 0.7416269779205322,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"timestamp" : "2025-07-19T12:02:38Z",
"isBreathing" : true,
"hasOscillation" : true,
"pressure" : 16.023325,
"currentState" : "starting",
"amplitudeHistory" : [
0.005,
1.0682217,
1.042688,
1.0682217
],
"timeSinceLastOscillation" : 0,
"breathDuration" : 0.7416269779205322,
"breathCount" : 2,
"audioLevel" : 0.07984167
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 52.07312,
"correlation" : 0.8028608,
"timestamp" : "2025-07-19T12:02:38Z",
"runLength" : 5,
"searchStrategy" : "economical",
"targetMaxFreq" : 21.111893,
"audioSamples" : 300,
"targetMinFreq" : 12.667136,
"detectedPitch" : 16.889515,
"audioLevel" : 0.0011738711,
"movingAveAmplitude" : 0.72117335,
"economicalSearchUsed" : true,
"bestPeriod" : 58,
"audioVariance" : 5.028788e-08,
"downsampledSamples" : 300,
"processingTimeMs" : 1.7399787902832031
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"averageAudioLevel" : 0.01582493,
"maxAudioLevel" : 0.09142592,
"processingStepTimeMs" : {
"downsampling" : 1.273036003112793,
"squaring" : 0.025987625122070312,
"filtering" : 236.90998554229736,
"smoothing" : 0.12195110321044922,
"autocorrelation" : 1.8439292907714844
},
"squaredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:38Z",
"downsampledSamples" : 97,
"minAudioLevel" : 2.8042123e-06,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 14.240366,
"detectedPitch" : 16.889515,
"frameCount" : 4800,
"audioLevel" : 0.09142592
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 16.889515,
"timeSinceBreathStart" : 0.9884339570999146,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"timestamp" : "2025-07-19T12:02:38Z",
"isBreathing" : true,
"hasOscillation" : true,
"pressure" : 14.240366,
"currentState" : "starting",
"amplitudeHistory" : [
0.005,
1.0682217,
1.042688,
1.0682217,
0.94935775
],
"timeSinceLastOscillation" : 0,
"breathDuration" : 0.9884339570999146,
"breathCount" : 2,
"audioLevel" : 0.09142592
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 52.858498,
"correlation" : 0.745818,
"timestamp" : "2025-07-19T12:02:38Z",
"runLength" : 5,
"searchStrategy" : "economical",
"targetMaxFreq" : 21.86589,
"audioSamples" : 300,
"targetMinFreq" : 13.119534,
"detectedPitch" : 17.492712,
"audioLevel" : 0.0014294764,
"movingAveAmplitude" : 0.7261023,
"economicalSearchUsed" : true,
"bestPeriod" : 56,
"audioVariance" : 6.775365e-08,
"downsampledSamples" : 300,
"processingTimeMs" : 1.8749237060546875
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.099879056,
"squaredSamples" : 4800,
"minAudioLevel" : 4.190486e-06,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"autocorrelation" : 1.9739866256713867,
"filtering" : 237.4870777130127,
"downsampling" : 1.2769699096679688,
"smoothing" : 0.12195110321044922
},
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:38Z",
"averageAudioLevel" : 0.016096467,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 14.915344,
"audioLevel" : 0.099879056,
"detectedPitch" : 17.492712
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 17.492712,
"timeSinceBreathStart" : 1.2359880208969116,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:38Z",
"pressure" : 14.915344,
"hasOscillation" : true,
"currentState" : "starting",
"amplitudeHistory" : [
0.005,
1.0682217,
1.042688,
1.0682217,
0.94935775,
0.9943563
],
"audioLevel" : 0.099879056,
"breathDuration" : 1.2359880208969116,
"breathCount" : 2,
"timeSinceLastOscillation" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 53.4868,
"correlation" : 0.75969094,
"timestamp" : "2025-07-19T12:02:38Z",
"runLength" : 5,
"searchStrategy" : "economical",
"targetMaxFreq" : 21.86589,
"audioSamples" : 300,
"targetMinFreq" : 13.119534,
"detectedPitch" : 17.492712,
"audioLevel" : 0.0014294764,
"movingAveAmplitude" : 0.73282003,
"economicalSearchUsed" : true,
"bestPeriod" : 56,
"audioVariance" : 7.378803e-08,
"downsampledSamples" : 300,
"processingTimeMs" : 1.8929243087768555
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.098951206,
"squaredSamples" : 4800,
"minAudioLevel" : 1.6067643e-06,
"processingStepTimeMs" : {
"autocorrelation" : 1.9919872283935547,
"downsampling" : 1.2890100479125977,
"filtering" : 236.69803142547607,
"squaring" : 0.02396106719970703,
"smoothing" : 0.12099742889404297
},
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:38Z",
"averageAudioLevel" : 0.01585898,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 14.915344,
"frameCount" : 4800,
"audioLevel" : 0.098951206,
"detectedPitch" : 17.492712
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:38Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 17.492712,
"timeSinceBreathStart" : 1.482801914215088,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:38Z",
"pressure" : 14.915344,
"hasOscillation" : true,
"currentState" : "starting",
"timeSinceLastOscillation" : 0,
"amplitudeHistory" : [
0.005,
1.0682217,
1.042688,
1.0682217,
0.94935775,
0.9943563,
0.9943563
],
"breathDuration" : 1.482801914215088,
"breathCount" : 2,
"audioLevel" : 0.098951206
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 53.98944,
"correlation" : 0.71990293,
"timestamp" : "2025-07-19T12:02:39Z",
"runLength" : 5,
"searchStrategy" : "economical",
"targetMaxFreq" : 21.86589,
"audioSamples" : 300,
"targetMinFreq" : 13.119534,
"detectedPitch" : 17.492712,
"audioLevel" : 0.0014294764,
"movingAveAmplitude" : 0.7302366,
"economicalSearchUsed" : true,
"bestPeriod" : 56,
"audioVariance" : 7.905643e-08,
"downsampledSamples" : 300,
"processingTimeMs" : 1.8869638442993164
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.095934376,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:39Z",
"downsampledSamples" : 97,
"minAudioLevel" : 7.511815e-06,
"averageAudioLevel" : 0.015207691,
"processingStepTimeMs" : {
"smoothing" : 0.12195110321044922,
"filtering" : 237.31601238250732,
"autocorrelation" : 1.992940902709961,
"squaring" : 0.024080276489257812,
"downsampling" : 1.280069351196289
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.095934376,
"frameCount" : 4800,
"detectedPitch" : 17.492712,
"calculatedPressure" : 14.915344
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 17.492712,
"timeSinceBreathStart" : 1.7302119731903076,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:39Z",
"pressure" : 14.915344,
"hasOscillation" : true,
"currentState" : "active",
"amplitudeHistory" : [
0.005,
1.0682217,
1.042688,
1.0682217,
0.94935775,
0.9943563,
0.9943563,
0.9943563
],
"audioLevel" : 0.095934376,
"breathDuration" : 1.7302119731903076,
"breathCount" : 2,
"timeSinceLastOscillation" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [PITCH] Pitch detection analysis
DATA: {
"correlation" : 0.7092693,
"audioVariance" : 9.392986e-08,
"audioLevel" : 0.0017794155,
"movingAvePeriod" : 54.991554,
"economicalSearchUsed" : true,
"targetMinFreq" : 12.452439,
"searchStrategy" : "economical",
"detectedPitch" : 16.603252,
"targetMaxFreq" : 20.754066,
"bestPeriod" : 59,
"movingAveAmplitude" : 0.72604316,
"runLength" : 5,
"processingTimeMs" : 1.909017562866211,
"audioSamples" : 300,
"timestamp" : "2025-07-19T12:02:39Z",
"downsampledSamples" : 300
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.089011505,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:39Z",
"downsampledSamples" : 97,
"minAudioLevel" : 4.953705e-06,
"averageAudioLevel" : 0.017635852,
"processingStepTimeMs" : {
"filtering" : 237.37502098083496,
"smoothing" : 0.12505054473876953,
"squaring" : 0.012040138244628906,
"downsampling" : 1.3020038604736328,
"autocorrelation" : 2.0099878311157227
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 16.603252,
"calculatedPressure" : 13.920038,
"audioLevel" : 0.089011505,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [BREATH] Breath detection analysis
DATA: {
"audioLevel" : 0.089011505,
"timeSinceLastOscillation" : 0,
"frequency" : 16.603252,
"isBreathing" : true,
"hasOscillation" : true,
"amplitudeHistory" : [
0.005,
1.0682217,
1.042688,
1.0682217,
0.94935775,
0.9943563,
0.9943563,
0.9943563,
0.92800254
],
"pressure" : 13.920038,
"currentState" : "active",
"timestamp" : "2025-07-19T12:02:39Z",
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"breathDuration" : 1.97770094871521,
"timeSinceBreathStart" : 1.97770094871521,
"breathCount" : 2
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [PITCH] Pitch detection analysis
DATA: {
"correlation" : 0.6672818,
"audioVariance" : 9.78132e-08,
"audioLevel" : 0.0017794155,
"movingAvePeriod" : 55.993244,
"economicalSearchUsed" : true,
"targetMinFreq" : 12.244898,
"searchStrategy" : "economical",
"detectedPitch" : 16.32653,
"targetMaxFreq" : 20.408163,
"bestPeriod" : 60,
"movingAveAmplitude" : 0.7142909,
"runLength" : 5,
"processingTimeMs" : 1.885056495666504,
"audioSamples" : 300,
"timestamp" : "2025-07-19T12:02:39Z",
"downsampledSamples" : 300
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.110897556,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:39Z",
"downsampledSamples" : 97,
"minAudioLevel" : 1.5017577e-07,
"averageAudioLevel" : 0.015461182,
"processingStepTimeMs" : {
"autocorrelation" : 1.9849538803100586,
"downsampling" : 1.3030767440795898,
"filtering" : 236.9849681854248,
"squaring" : 0.012040138244628906,
"smoothing" : 0.1220703125
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 13.610386,
"frameCount" : 4800,
"detectedPitch" : 16.32653,
"audioLevel" : 0.110897556
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : true,
"timeSinceLastOscillation" : 0,
"frequency" : 16.32653,
"audioLevel" : 0.110897556,
"hasOscillation" : true,
"amplitudeHistory" : [
0.005,
1.0682217,
1.042688,
1.0682217,
0.94935775,
0.9943563,
0.9943563,
0.9943563,
0.92800254,
0.90735906
],
"timestamp" : "2025-07-19T12:02:39Z",
"pressure" : 13.610386,
"currentState" : "active",
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"breathDuration" : 2.224745988845825,
"timeSinceBreathStart" : 2.224745988845825,
"breathCount" : 2
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [PITCH] Pitch detection analysis
DATA: {
"audioLevel" : 0.0017794155,
"detectedPitch" : 13.605442,
"processingTimeMs" : 2.035975456237793,
"movingAveAmplitude" : 0.70649964,
"bestPeriod" : 72,
"runLength" : 5,
"timestamp" : "2025-07-19T12:02:39Z",
"economicalSearchUsed" : true,
"downsampledSamples" : 300,
"correlation" : 0.6753344,
"searchStrategy" : "economical",
"targetMaxFreq" : 17.006802,
"targetMinFreq" : 10.204082,
"audioSamples" : 300,
"movingAvePeriod" : 59.194595,
"audioVariance" : 9.390702e-08
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.09136243,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:39Z",
"downsampledSamples" : 97,
"minAudioLevel" : 5.732989e-06,
"averageAudioLevel" : 0.015077003,
"processingStepTimeMs" : {
"smoothing" : 0.12099742889404297,
"autocorrelation" : 2.1489858627319336,
"filtering" : 237.2879981994629,
"downsampling" : 1.2760162353515625,
"squaring" : 0.012993812561035156
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"audioLevel" : 0.09136243,
"calculatedPressure" : 10.565489,
"detectedPitch" : 13.605442
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:39Z] [BREATH] Breath detection analysis
DATA: {
"breathCount" : 2,
"amplitudeHistory" : [
1.0682217,
1.042688,
1.0682217,
0.94935775,
0.9943563,
0.9943563,
0.9943563,
0.92800254,
0.90735906,
0.7043659
],
"currentState" : "active",
"timeSinceLastOscillation" : 0,
"hasOscillation" : true,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"pressure" : 10.565489,
"frequency" : 13.605442,
"breathDuration" : 2.4722189903259277,
"timestamp" : "2025-07-19T12:02:39Z",
"timeSinceBreathStart" : 2.4722189903259277,
"isBreathing" : true,
"audioLevel" : 0.09136243
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [PITCH] Pitch detection analysis
DATA: {
"economicalSearchUsed" : true,
"processingTimeMs" : 2.246975898742676,
"movingAveAmplitude" : 0.6976638,
"runLength" : 5,
"audioSamples" : 300,
"detectedPitch" : 16.603252,
"movingAvePeriod" : 59.155678,
"downsampledSamples" : 300,
"timestamp" : "2025-07-19T12:02:40Z",
"targetMinFreq" : 12.452439,
"targetMaxFreq" : 20.754066,
"audioLevel" : 0.0017013988,
"searchStrategy" : "economical",
"bestPeriod" : 59,
"correlation" : 0.66232014,
"audioVariance" : 8.974635e-08
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"downsampling" : 1.314997673034668,
"squaring" : 0.025033950805664062,
"filtering" : 236.71495914459229,
"smoothing" : 0.1220703125,
"autocorrelation" : 2.3599863052368164
},
"maxAudioLevel" : 0.08788061,
"averageAudioLevel" : 0.016976848,
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:40Z",
"downsampledSamples" : 97,
"minAudioLevel" : 7.690396e-06,
"squaredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 13.920038,
"detectedPitch" : 16.603252,
"frameCount" : 4800,
"audioLevel" : 0.08788061
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [BREATH] Breath detection analysis
DATA: {
"pressure" : 13.920038,
"hasOscillation" : true,
"timestamp" : "2025-07-19T12:02:40Z",
"audioLevel" : 0.08788061,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"frequency" : 16.603252,
"amplitudeHistory" : [
1.042688,
1.0682217,
0.94935775,
0.9943563,
0.9943563,
0.9943563,
0.92800254,
0.90735906,
0.7043659,
0.92800254
],
"currentState" : "active",
"breathCount" : 2,
"isBreathing" : true,
"breathDuration" : 2.7194669246673584,
"timeSinceLastOscillation" : 0,
"timeSinceBreathStart" : 2.7194669246673584
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [PITCH] Pitch detection analysis
DATA: {
"detectedPitch" : 17.81076,
"targetMaxFreq" : 22.26345,
"correlation" : 0.65395063,
"bestPeriod" : 55,
"movingAvePeriod" : 58.324543,
"economicalSearchUsed" : true,
"timestamp" : "2025-07-19T12:02:40Z",
"processingTimeMs" : 1.9059181213378906,
"targetMinFreq" : 13.35807,
"movingAveAmplitude" : 0.68892115,
"audioLevel" : 0.0018249627,
"audioSamples" : 300,
"downsampledSamples" : 300,
"audioVariance" : 9.2090325e-08,
"runLength" : 5,
"searchStrategy" : "economical"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.10185907,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:40Z",
"downsampledSamples" : 97,
"minAudioLevel" : 1.908117e-05,
"averageAudioLevel" : 0.015333072,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"filtering" : 237.1770143508911,
"downsampling" : 1.2900829315185547,
"autocorrelation" : 2.007007598876953,
"smoothing" : 0.12195110321044922
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.10185907,
"detectedPitch" : 17.81076,
"calculatedPressure" : 15.27124,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"timeSinceBreathStart" : 2.966760993003845,
"amplitudeHistory" : [
1.0682217,
0.94935775,
0.9943563,
0.9943563,
0.9943563,
0.92800254,
0.90735906,
0.7043659,
0.92800254,
1.0180827
],
"currentState" : "active",
"pressure" : 15.27124,
"timestamp" : "2025-07-19T12:02:40Z",
"hasOscillation" : true,
"audioLevel" : 0.10185907,
"breathDuration" : 2.966760993003845,
"breathCount" : 2,
"frequency" : 17.81076,
"timeSinceLastOscillation" : 0,
"isBreathing" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [PITCH] Pitch detection analysis
DATA: {
"bestPeriod" : 57,
"targetMaxFreq" : 21.482277,
"targetMinFreq" : 12.889366,
"searchStrategy" : "economical",
"timestamp" : "2025-07-19T12:02:40Z",
"correlation" : 0.667541,
"audioVariance" : 8.9405134e-08,
"audioLevel" : 0.0018249627,
"processingTimeMs" : 1.891016960144043,
"economicalSearchUsed" : true,
"movingAvePeriod" : 58.05963,
"movingAveAmplitude" : 0.6846451,
"audioSamples" : 300,
"detectedPitch" : 17.185822,
"downsampledSamples" : 300,
"runLength" : 5
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.072532356,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:40Z",
"downsampledSamples" : 97,
"minAudioLevel" : 5.7057478e-06,
"averageAudioLevel" : 0.014345799,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"filtering" : 236.69695854187012,
"autocorrelation" : 1.9900798797607422,
"downsampling" : 1.2879371643066406,
"smoothing" : 0.12695789337158203
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.072532356,
"frameCount" : 4800,
"detectedPitch" : 17.185822,
"calculatedPressure" : 14.571934
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [BREATH] Breath detection analysis
DATA: {
"amplitudeHistory" : [
0.94935775,
0.9943563,
0.9943563,
0.9943563,
0.92800254,
0.90735906,
0.7043659,
0.92800254,
1.0180827,
0.97146225
],
"isBreathing" : true,
"breathCount" : 2,
"frequency" : 17.185822,
"audioLevel" : 0.072532356,
"currentState" : "active",
"timestamp" : "2025-07-19T12:02:40Z",
"timeSinceBreathStart" : 3.2135099172592163,
"hasOscillation" : true,
"breathDuration" : 3.2135099172592163,
"timeSinceLastOscillation" : 0,
"pressure" : 14.571934,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 1.8919706344604492,
"economicalSearchUsed" : true,
"correlation" : 0.7250607,
"audioLevel" : 0.0018249627,
"downsampledSamples" : 300,
"detectedPitch" : 16.603252,
"timestamp" : "2025-07-19T12:02:40Z",
"targetMaxFreq" : 20.754066,
"searchStrategy" : "economical",
"movingAvePeriod" : 58.247704,
"runLength" : 5,
"targetMinFreq" : 12.452439,
"bestPeriod" : 59,
"audioSamples" : 300,
"audioVariance" : 6.588637e-08,
"movingAveAmplitude" : 0.6927282
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"downsampling" : 1.295924186706543,
"squaring" : 0.024080276489257812,
"filtering" : 236.71305179595947,
"smoothing" : 0.12099742889404297,
"autocorrelation" : 1.994013786315918
},
"maxAudioLevel" : 0.046908647,
"averageAudioLevel" : 0.00849965,
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:40Z",
"inputSamples" : 4800,
"minAudioLevel" : 1.7718412e-07,
"squaredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 13.920038,
"detectedPitch" : 16.603252,
"audioLevel" : 0.046908647
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:40Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"breathDuration" : 3.460330009460449,
"timeSinceBreathStart" : 3.460330009460449,
"currentState" : "active",
"breathCount" : 2,
"timeSinceLastOscillation" : 0,
"audioLevel" : 0.046908647,
"pressure" : 13.920038,
"timestamp" : "2025-07-19T12:02:40Z",
"amplitudeHistory" : [
0.9943563,
0.9943563,
0.9943563,
0.92800254,
0.90735906,
0.7043659,
0.92800254,
1.0180827,
0.97146225,
0.92800254
],
"frequency" : 16.603252,
"hasOscillation" : true,
"isBreathing" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [PITCH] Pitch detection analysis
DATA: {
"downsampledSamples" : 300,
"detectedPitch" : 17.81076,
"bestPeriod" : 55,
"correlation" : 0.6538509,
"audioSamples" : 300,
"audioVariance" : 3.1935485e-08,
"economicalSearchUsed" : true,
"movingAvePeriod" : 57.598164,
"processingTimeMs" : 1.9019842147827148,
"targetMaxFreq" : 22.26345,
"audioLevel" : 0.00093998597,
"movingAveAmplitude" : 0.68495274,
"timestamp" : "2025-07-19T12:02:41Z",
"targetMinFreq" : 13.35807,
"searchStrategy" : "economical",
"runLength" : 5
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.017240612,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:41Z",
"downsampledSamples" : 97,
"minAudioLevel" : 5.092079e-08,
"averageAudioLevel" : 0.0013467313,
"processingStepTimeMs" : {
"filtering" : 239.2439842224121,
"downsampling" : 1.2949705123901367,
"squaring" : 0.02396106719970703,
"autocorrelation" : 2.0090341567993164,
"smoothing" : 0.12302398681640625
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"audioLevel" : 0.017240612,
"detectedPitch" : 17.81076,
"calculatedPressure" : 15.27124
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [BREATH] Breath detection analysis
DATA: {
"currentState" : "active",
"isBreathing" : true,
"timeSinceLastOscillation" : 0,
"timeSinceBreathStart" : 3.709715962409973,
"audioLevel" : 0.017240612,
"breathCount" : 2,
"breathDuration" : 3.709715962409973,
"frequency" : 17.81076,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"hasOscillation" : true,
"pressure" : 15.27124,
"amplitudeHistory" : [
0.9943563,
0.9943563,
0.92800254,
0.90735906,
0.7043659,
0.92800254,
1.0180827,
0.97146225,
0.92800254,
1.0180827
],
"timestamp" : "2025-07-19T12:02:41Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 1.1304968e-08,
"processingTimeMs" : 5.102038383483887,
"audioLevel" : 0.0006571382,
"targetMinFreq" : 7,
"economicalSearchUsed" : false,
"downsampledSamples" : 300,
"targetMaxFreq" : 9.718173,
"runLength" : 4,
"correlation" : 0.65748775,
"movingAveAmplitude" : 0.6780865,
"audioSamples" : 300,
"detectedPitch" : 7.7745385,
"bestPeriod" : 126,
"searchStrategy" : "fallback",
"timestamp" : "2025-07-19T12:02:41Z",
"movingAvePeriod" : 74.69862
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"smoothing" : 0.1360177993774414,
"downsampling" : 1.2720823287963867,
"autocorrelation" : 5.208015441894531,
"filtering" : 236.84704303741455,
"squaring" : 0.011920928955078125
},
"maxAudioLevel" : 0.0017288777,
"averageAudioLevel" : 0.00044520834,
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:41Z",
"downsampledSamples" : 97,
"minAudioLevel" : 2.1139385e-08,
"squaredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"audioLevel" : 0.0017288777,
"calculatedPressure" : 4.040708,
"detectedPitch" : 7.7745385
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [BREATH] Breath detection analysis
DATA: {
"timeSinceBreathStart" : 3.959844946861267,
"frequency" : 7.7745385,
"hasOscillation" : true,
"timestamp" : "2025-07-19T12:02:41Z",
"breathDuration" : 3.959844946861267,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"isBreathing" : true,
"breathCount" : 2,
"currentState" : "active",
"pressure" : 4.040708,
"amplitudeHistory" : [
0.9943563,
0.92800254,
0.90735906,
0.7043659,
0.92800254,
1.0180827,
0.97146225,
0.92800254,
1.0180827,
0.26938054
],
"timeSinceLastOscillation" : 0,
"audioLevel" : 0.0017288777
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [PITCH] Pitch detection analysis
DATA: {
"targetMinFreq" : 7.5741644,
"detectedPitch" : 10.098886,
"economicalSearchUsed" : false,
"bestPeriod" : 97,
"timestamp" : "2025-07-19T12:02:41Z",
"targetMaxFreq" : 12.623607,
"audioSamples" : 300,
"movingAveAmplitude" : 0.7031257,
"movingAvePeriod" : 80.27397,
"runLength" : 4,
"searchStrategy" : "fallback",
"audioVariance" : 7.29686e-10,
"processingTimeMs" : 5.478978157043457,
"downsampledSamples" : 300,
"audioLevel" : 0.00025816492,
"correlation" : 0.7782432
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0018935261,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:41Z",
"downsampledSamples" : 97,
"minAudioLevel" : 4.348476e-08,
"averageAudioLevel" : 0.00047196474,
"processingStepTimeMs" : {
"autocorrelation" : 5.586981773376465,
"downsampling" : 1.2700557708740234,
"smoothing" : 0.14197826385498047,
"filtering" : 236.70494556427002,
"squaring" : 0.012993812561035156
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0018935261,
"detectedPitch" : 10.098886,
"calculatedPressure" : 6.6416526,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"currentState" : "active",
"hasOscillation" : true,
"breathDuration" : 4.210196018218994,
"frequency" : 10.098886,
"pressure" : 6.6416526,
"amplitudeHistory" : [
0.92800254,
0.90735906,
0.7043659,
0.92800254,
1.0180827,
0.97146225,
0.92800254,
1.0180827,
0.26938054,
0.44277683
],
"timeSinceLastOscillation" : 0,
"timeSinceBreathStart" : 4.210196018218994,
"isBreathing" : true,
"audioLevel" : 0.0018935261,
"breathCount" : 2,
"timestamp" : "2025-07-19T12:02:41Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"smoothing" : 0.1220703125,
"downsampling" : 1.2660026550292969,
"filtering" : 236.5480661392212,
"squaring" : 0.012993812561035156,
"autocorrelation" : 0.1900196075439453
},
"maxAudioLevel" : 0.001994564,
"averageAudioLevel" : 0.00046336034,
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:41Z",
"minAudioLevel" : 1.3272893e-07,
"squaredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.001994564
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:41Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"currentState" : "active",
"hasOscillation" : false,
"breathDuration" : 4.454965949058533,
"frequency" : 0,
"pressure" : 0,
"amplitudeHistory" : [
0.92800254,
0.90735906,
0.7043659,
0.92800254,
1.0180827,
0.97146225,
0.92800254,
1.0180827,
0.26938054,
0.44277683
],
"timeSinceLastOscillation" : 0.24476993083953857,
"timeSinceBreathStart" : 4.454965949058533,
"isBreathing" : true,
"audioLevel" : 0.001994564,
"breathCount" : 2,
"timestamp" : "2025-07-19T12:02:41Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.002005995,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:42Z",
"downsampledSamples" : 97,
"minAudioLevel" : 9.847645e-08,
"averageAudioLevel" : 0.00046415007,
"processingStepTimeMs" : {
"squaring" : 0.011920928955078125,
"filtering" : 237.0389699935913,
"downsampling" : 1.2869834899902344,
"smoothing" : 0.12195110321044922,
"autocorrelation" : 0.1939535140991211
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.002005995,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"currentState" : "ending",
"hasOscillation" : false,
"breathDuration" : 4.700299024581909,
"frequency" : 0,
"pressure" : 0,
"amplitudeHistory" : [
0.92800254,
0.90735906,
0.7043659,
0.92800254,
1.0180827,
0.97146225,
0.92800254,
1.0180827,
0.26938054,
0.44277683
],
"timeSinceLastOscillation" : 0.49010300636291504,
"timeSinceBreathStart" : 4.700299024581909,
"isBreathing" : true,
"audioLevel" : 0.002005995,
"breathCount" : 2,
"timestamp" : "2025-07-19T12:02:42Z"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0016421983,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:42Z",
"downsampledSamples" : 97,
"minAudioLevel" : 6.622804e-08,
"averageAudioLevel" : 0.00046006852,
"processingStepTimeMs" : {
"smoothing" : 0.13303756713867188,
"autocorrelation" : 0.19991397857666016,
"squaring" : 0.012993812561035156,
"downsampling" : 1.2949705123901367,
"filtering" : 240.47505855560303
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"detectedPitch" : 0,
"calculatedPressure" : 0,
"audioLevel" : 0.0016421983
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"timeSinceBreathStart" : 4.9490439891815186,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:42Z",
"isBreathing" : false,
"hasOscillation" : false,
"pressure" : 0,
"currentState" : "completed",
"amplitudeHistory" : [

],
"timeSinceLastOscillation" : 0.7388479709625244,
"breathDuration" : 0,
"breathCount" : 3,
"audioLevel" : 0.0016421983
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [AUDIO] Audio processing pipeline
DATA: {
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"maxAudioLevel" : 0.0037162537,
"averageAudioLevel" : 0.0004351604,
"timestamp" : "2025-07-19T12:02:42Z",
"minAudioLevel" : 3.875175e-08,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"squaring" : 0.02300739288330078,
"smoothing" : 0.1220703125,
"downsampling" : 1.2689828872680664,
"filtering" : 236.96398735046387,
"autocorrelation" : 0.19299983978271484
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.0037162537,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : false,
"breathCount" : 3,
"amplitudeHistory" : [

],
"pressure" : 0,
"breathDuration" : 0,
"currentState" : "idle",
"frequency" : 0,
"audioLevel" : 0.0037162537,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:42Z",
"hasOscillation" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 71.515976,
"correlation" : 0.64835757,
"timestamp" : "2025-07-19T12:02:42Z",
"runLength" : 3,
"searchStrategy" : "fallback",
"targetMaxFreq" : 22.67574,
"audioSamples" : 300,
"targetMinFreq" : 13.605443,
"detectedPitch" : 18.14059,
"audioLevel" : 0.00024069357,
"movingAveAmplitude" : 0.68486965,
"economicalSearchUsed" : false,
"bestPeriod" : 54,
"audioVariance" : 6.184194e-10,
"downsampledSamples" : 300,
"processingTimeMs" : 5.856037139892578
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"filteredSamples" : 4800,
"maxAudioLevel" : 0.026963726,
"averageAudioLevel" : 0.0027248384,
"timestamp" : "2025-07-19T12:02:42Z",
"minAudioLevel" : 7.0163514e-07,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"downsampling" : 1.3120174407958984,
"smoothing" : 0.1220703125,
"autocorrelation" : 5.979061126708984,
"squaring" : 0.013113021850585938,
"filtering" : 239.28403854370117
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.026963726,
"calculatedPressure" : 15.64032,
"detectedPitch" : 18.14059,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:42Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : true,
"breathCount" : 3,
"amplitudeHistory" : [
0.005
],
"pressure" : 15.64032,
"breathDuration" : 0,
"currentState" : "starting",
"frequency" : 18.14059,
"audioLevel" : 0.026963726,
"timeSinceLastOscillation" : 0,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"timestamp" : "2025-07-19T12:02:42Z",
"hasOscillation" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 71.515976,
"correlation" : 0.56530017,
"timestamp" : "2025-07-19T12:02:43Z",
"runLength" : 3,
"searchStrategy" : "fallback",
"targetMaxFreq" : 22.67574,
"audioSamples" : 300,
"targetMinFreq" : 13.605443,
"detectedPitch" : 0,
"audioLevel" : 0.00033387615,
"movingAveAmplitude" : 0.68486965,
"economicalSearchUsed" : false,
"bestPeriod" : 59,
"audioVariance" : 2.7590623e-09,
"downsampledSamples" : 300,
"processingTimeMs" : 5.033969879150391
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"downsampling" : 1.2669563293457031,
"squaring" : 0.012993812561035156,
"smoothing" : 0.12111663818359375,
"autocorrelation" : 5.138993263244629,
"filtering" : 236.80198192596436
},
"maxAudioLevel" : 0.041171715,
"averageAudioLevel" : 0.005756455,
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"inputSamples" : 4800,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:43Z",
"minAudioLevel" : 1.9965228e-07,
"squaredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.041171715
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"timeSinceBreathStart" : 0.250014066696167,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:43Z",
"pressure" : 0,
"hasOscillation" : true,
"currentState" : "starting",
"timeSinceLastOscillation" : 0,
"amplitudeHistory" : [
0.005,
0.041171715
],
"breathDuration" : 0.250014066696167,
"breathCount" : 3,
"audioLevel" : 0.041171715
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 65.34398,
"correlation" : 0.6529884,
"timestamp" : "2025-07-19T12:02:43Z",
"runLength" : 3,
"searchStrategy" : "economical",
"targetMaxFreq" : 23.10358,
"audioSamples" : 300,
"targetMinFreq" : 13.862148,
"detectedPitch" : 18.482864,
"audioLevel" : 0.00033387615,
"movingAveAmplitude" : 0.67424256,
"economicalSearchUsed" : true,
"bestPeriod" : 53,
"audioVariance" : 4.59785e-09,
"downsampledSamples" : 300,
"processingTimeMs" : 1.7750263214111328
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.047512762,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:43Z",
"downsampledSamples" : 97,
"minAudioLevel" : 4.484873e-07,
"averageAudioLevel" : 0.006078185,
"processingStepTimeMs" : {
"autocorrelation" : 1.891016960144043,
"squaring" : 0.012993812561035156,
"downsampling" : 1.2871026992797852,
"smoothing" : 0.12302398681640625,
"filtering" : 237.33103275299072
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.047512762,
"frameCount" : 4800,
"detectedPitch" : 18.482864,
"calculatedPressure" : 16.023325
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 18.482864,
"timeSinceBreathStart" : 0.49736297130584717,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"timestamp" : "2025-07-19T12:02:43Z",
"isBreathing" : true,
"hasOscillation" : true,
"pressure" : 16.023325,
"currentState" : "starting",
"amplitudeHistory" : [
0.005,
0.041171715,
1.0682217
],
"timeSinceLastOscillation" : 0,
"breathDuration" : 0.49736297130584717,
"breathCount" : 3,
"audioLevel" : 0.047512762
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 61.229317,
"correlation" : 0.73084867,
"timestamp" : "2025-07-19T12:02:43Z",
"runLength" : 3,
"searchStrategy" : "economical",
"targetMaxFreq" : 23.10358,
"audioSamples" : 300,
"targetMinFreq" : 13.862148,
"detectedPitch" : 18.482864,
"audioLevel" : 0.00033387615,
"movingAveAmplitude" : 0.6931113,
"economicalSearchUsed" : true,
"bestPeriod" : 53,
"audioVariance" : 4.935984e-09,
"downsampledSamples" : 300,
"processingTimeMs" : 1.7789602279663086
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [AUDIO] Audio processing pipeline
DATA: {
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"maxAudioLevel" : 0.038762353,
"averageAudioLevel" : 0.0060153417,
"timestamp" : "2025-07-19T12:02:43Z",
"minAudioLevel" : 9.67877e-07,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"smoothing" : 0.12195110321044922,
"downsampling" : 1.2710094451904297,
"filtering" : 237.06793785095215,
"squaring" : 0.012993812561035156,
"autocorrelation" : 1.8860101699829102
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.038762353,
"frameCount" : 4800,
"calculatedPressure" : 16.023325,
"detectedPitch" : 18.482864
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 18.482864,
"timeSinceBreathStart" : 0.7443810701370239,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:43Z",
"pressure" : 16.023325,
"hasOscillation" : true,
"currentState" : "starting",
"audioLevel" : 0.038762353,
"amplitudeHistory" : [
0.005,
0.041171715,
1.0682217,
1.0682217
],
"breathDuration" : 0.7443810701370239,
"breathCount" : 3,
"timeSinceLastOscillation" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 57.819542,
"correlation" : 0.76642585,
"timestamp" : "2025-07-19T12:02:43Z",
"runLength" : 3,
"searchStrategy" : "economical",
"targetMaxFreq" : 24.009605,
"audioSamples" : 300,
"targetMinFreq" : 14.405763,
"detectedPitch" : 19.207684,
"audioLevel" : 0.00033896154,
"movingAveAmplitude" : 0.71754944,
"economicalSearchUsed" : true,
"bestPeriod" : 51,
"audioVariance" : 5.5004263e-09,
"downsampledSamples" : 300,
"processingTimeMs" : 1.7529726028442383
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"autocorrelation" : 1.8569231033325195,
"squaring" : 0.02396106719970703,
"smoothing" : 0.13196468353271484,
"downsampling" : 1.273036003112793,
"filtering" : 236.7269992828369
},
"maxAudioLevel" : 0.04128341,
"averageAudioLevel" : 0.006517884,
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:43Z",
"minAudioLevel" : 1.0731164e-06,
"squaredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"audioLevel" : 0.04128341,
"calculatedPressure" : 16.834396,
"detectedPitch" : 19.207684
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:43Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 19.207684,
"timeSinceBreathStart" : 0.9910269975662231,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"timestamp" : "2025-07-19T12:02:43Z",
"isBreathing" : true,
"hasOscillation" : true,
"pressure" : 16.834396,
"currentState" : "starting",
"amplitudeHistory" : [
0.005,
0.041171715,
1.0682217,
1.0682217,
1.1222931
],
"timeSinceLastOscillation" : 0,
"breathDuration" : 0.9910269975662231,
"breathCount" : 3,
"audioLevel" : 0.04128341
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 55.864655,
"correlation" : 0.6787137,
"timestamp" : "2025-07-19T12:02:44Z",
"runLength" : 4,
"searchStrategy" : "economical",
"targetMaxFreq" : 24.489796,
"audioSamples" : 300,
"targetMinFreq" : 14.693878,
"detectedPitch" : 19.591837,
"audioLevel" : 0.00059606624,
"movingAveAmplitude" : 0.70784056,
"economicalSearchUsed" : true,
"bestPeriod" : 50,
"audioVariance" : 8.164541e-09,
"downsampledSamples" : 300,
"processingTimeMs" : 7.382988929748535
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"filteredSamples" : 4800,
"maxAudioLevel" : 0.045725986,
"averageAudioLevel" : 0.0062651606,
"timestamp" : "2025-07-19T12:02:44Z",
"minAudioLevel" : 9.4455027e-07,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"autocorrelation" : 7.483005523681641,
"downsampling" : 1.2680292129516602,
"smoothing" : 0.12195110321044922,
"filtering" : 237.341046333313
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.045725986,
"frameCount" : 4800,
"calculatedPressure" : 17.264265,
"detectedPitch" : 19.591837
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 19.591837,
"timeSinceBreathStart" : 1.2438490390777588,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:44Z",
"pressure" : 17.264265,
"hasOscillation" : true,
"currentState" : "starting",
"audioLevel" : 0.045725986,
"amplitudeHistory" : [
0.005,
0.041171715,
1.0682217,
1.0682217,
1.1222931,
1.150951
],
"breathDuration" : 1.2438490390777588,
"breathCount" : 3,
"timeSinceLastOscillation" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 57.491722,
"correlation" : 0.6546337,
"timestamp" : "2025-07-19T12:02:44Z",
"runLength" : 5,
"searchStrategy" : "fallback",
"targetMaxFreq" : 19.132654,
"audioSamples" : 300,
"targetMinFreq" : 11.479592,
"detectedPitch" : 15.306123,
"audioLevel" : 0.0006324783,
"movingAveAmplitude" : 0.69719917,
"economicalSearchUsed" : false,
"bestPeriod" : 64,
"audioVariance" : 1.0144743e-08,
"downsampledSamples" : 300,
"processingTimeMs" : 5.113959312438965
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"filteredSamples" : 4800,
"maxAudioLevel" : 0.04537194,
"averageAudioLevel" : 0.0065280558,
"timestamp" : "2025-07-19T12:02:44Z",
"minAudioLevel" : 1.9694562e-07,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"smoothing" : 0.1310110092163086,
"autocorrelation" : 5.218029022216797,
"filtering" : 236.8379831314087,
"downsampling" : 1.2840032577514648,
"squaring" : 0.02396106719970703
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 12.46855,
"audioLevel" : 0.04537194,
"frameCount" : 4800,
"detectedPitch" : 15.306123
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 15.306123,
"timeSinceBreathStart" : 1.494027018547058,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:44Z",
"pressure" : 12.46855,
"hasOscillation" : true,
"currentState" : "starting",
"timeSinceLastOscillation" : 0,
"amplitudeHistory" : [
0.005,
0.041171715,
1.0682217,
1.0682217,
1.1222931,
1.150951,
0.83123666
],
"breathDuration" : 1.494027018547058,
"breathCount" : 3,
"audioLevel" : 0.04537194
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 58.593376,
"correlation" : 0.60920364,
"timestamp" : "2025-07-19T12:02:44Z",
"runLength" : 5,
"searchStrategy" : "economical",
"targetMaxFreq" : 19.436346,
"audioSamples" : 300,
"targetMinFreq" : 11.661808,
"detectedPitch" : 15.549077,
"audioLevel" : 0.0006324783,
"movingAveAmplitude" : 0.67960006,
"economicalSearchUsed" : true,
"bestPeriod" : 63,
"audioVariance" : 1.1383619e-08,
"downsampledSamples" : 300,
"processingTimeMs" : 1.9710063934326172
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [AUDIO] Audio processing pipeline
DATA: {
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"maxAudioLevel" : 0.04126541,
"averageAudioLevel" : 0.006227403,
"timestamp" : "2025-07-19T12:02:44Z",
"minAudioLevel" : 2.2370368e-06,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"squaring" : 0.02491474151611328,
"autocorrelation" : 2.0699501037597656,
"filtering" : 237.13302612304688,
"downsampling" : 1.278996467590332,
"smoothing" : 0.12505054473876953
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.04126541,
"frameCount" : 4800,
"detectedPitch" : 15.549077,
"calculatedPressure" : 12.740416
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 15.549077,
"timeSinceBreathStart" : 1.7413330078125,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"timestamp" : "2025-07-19T12:02:44Z",
"isBreathing" : true,
"hasOscillation" : true,
"pressure" : 12.740416,
"currentState" : "active",
"audioLevel" : 0.04126541,
"amplitudeHistory" : [
0.005,
0.041171715,
1.0682217,
1.0682217,
1.1222931,
1.150951,
0.83123666,
0.84936106
],
"timeSinceLastOscillation" : 0,
"breathCount" : 3,
"breathDuration" : 1.7413330078125
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 73.19503,
"correlation" : 0.683597,
"timestamp" : "2025-07-19T12:02:44Z",
"runLength" : 4,
"searchStrategy" : "fallback",
"targetMaxFreq" : 10.465725,
"audioSamples" : 300,
"targetMinFreq" : 7,
"detectedPitch" : 8.37258,
"audioLevel" : 0.0006324783,
"movingAveAmplitude" : 0.68059933,
"economicalSearchUsed" : false,
"bestPeriod" : 117,
"audioVariance" : 7.966825e-09,
"downsampledSamples" : 300,
"processingTimeMs" : 5.226016044616699
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [AUDIO] Audio processing pipeline
DATA: {
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"maxAudioLevel" : 0.03125164,
"averageAudioLevel" : 0.0043578595,
"timestamp" : "2025-07-19T12:02:44Z",
"minAudioLevel" : 3.6284182e-06,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"filtering" : 237.06400394439697,
"smoothing" : 0.12695789337158203,
"downsampling" : 1.2830495834350586,
"squaring" : 0.02396106719970703,
"autocorrelation" : 5.3299665451049805
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 4.7099166,
"audioLevel" : 0.03125164,
"detectedPitch" : 8.37258,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:44Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 8.37258,
"timeSinceBreathStart" : 1.991834044456482,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:44Z",
"hasOscillation" : true,
"pressure" : 4.7099166,
"currentState" : "active",
"timeSinceLastOscillation" : 0,
"audioLevel" : 0.03125164,
"breathDuration" : 1.991834044456482,
"amplitudeHistory" : [
0.005,
0.041171715,
1.0682217,
1.0682217,
1.1222931,
1.150951,
0.83123666,
0.84936106,
0.31399444
],
"breathCount" : 3
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [PITCH] Pitch detection analysis
DATA: {
"movingAvePeriod" : 73.19503,
"correlation" : 0.5445011,
"timestamp" : "2025-07-19T12:02:45Z",
"runLength" : 4,
"searchStrategy" : "fallback",
"targetMaxFreq" : 10.465725,
"audioSamples" : 300,
"targetMinFreq" : 7,
"detectedPitch" : 0,
"audioLevel" : 0.0005232799,
"movingAveAmplitude" : 0.68059933,
"economicalSearchUsed" : false,
"bestPeriod" : 70,
"audioVariance" : 5.4404907e-09,
"downsampledSamples" : 300,
"processingTimeMs" : 6.021976470947266
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"filteredSamples" : 4800,
"maxAudioLevel" : 0.029986016,
"averageAudioLevel" : 0.0043963064,
"timestamp" : "2025-07-19T12:02:45Z",
"minAudioLevel" : 9.165087e-07,
"downsampledSamples" : 97,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"squaring" : 0.024080276489257812,
"downsampling" : 1.2819766998291016,
"autocorrelation" : 6.123900413513184,
"smoothing" : 0.12099742889404297,
"filtering" : 237.13397979736328
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"detectedPitch" : 0,
"calculatedPressure" : 0,
"audioLevel" : 0.029986016
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"timeSinceBreathStart" : 2.2432340383529663,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:45Z",
"pressure" : 0,
"hasOscillation" : true,
"currentState" : "active",
"audioLevel" : 0.029986016,
"amplitudeHistory" : [
0.005,
0.041171715,
1.0682217,
1.0682217,
1.1222931,
1.150951,
0.83123666,
0.84936106,
0.31399444,
0.029986016
],
"breathDuration" : 2.2432340383529663,
"timeSinceLastOscillation" : 0,
"breathCount" : 3
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [PITCH] Pitch detection analysis
DATA: {
"runLength" : 3,
"bestPeriod" : 133,
"economicalSearchUsed" : true,
"targetMinFreq" : 7,
"targetMaxFreq" : 9.20669,
"processingTimeMs" : 2.727985382080078,
"timestamp" : "2025-07-19T12:02:45Z",
"detectedPitch" : 7.365352,
"audioVariance" : 3.1876013e-09,
"movingAveAmplitude" : 0.6931541,
"audioSamples" : 300,
"correlation" : 0.7182636,
"audioLevel" : 0.00036662156,
"movingAvePeriod" : 93.13002,
"searchStrategy" : "economical",
"downsampledSamples" : 300
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [AUDIO] Audio processing pipeline
DATA: {
"averageAudioLevel" : 0.0038309435,
"filteredSamples" : 4800,
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.024540713,
"minAudioLevel" : 1.768989e-06,
"timestamp" : "2025-07-19T12:02:45Z",
"squaredSamples" : 4800,
"inputSamples" : 4800,
"processingStepTimeMs" : {
"autocorrelation" : 2.8389692306518555,
"squaring" : 0.012040138244628906,
"filtering" : 237.29407787322998,
"smoothing" : 0.13303756713867188,
"downsampling" : 1.298069953918457
}
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"detectedPitch" : 7.365352,
"audioLevel" : 0.024540713,
"calculatedPressure" : 3.582829
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [BREATH] Breath detection analysis
DATA: {
"timeSinceBreathStart" : 2.491557002067566,
"timestamp" : "2025-07-19T12:02:45Z",
"amplitudeHistory" : [
0.041171715,
1.0682217,
1.0682217,
1.1222931,
1.150951,
0.83123666,
0.84936106,
0.31399444,
0.029986016,
0.23885527
],
"hasOscillation" : true,
"currentState" : "active",
"timeSinceLastOscillation" : 0,
"isBreathing" : true,
"breathDuration" : 2.491557002067566,
"frequency" : 7.365352,
"breathCount" : 3,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"pressure" : 3.582829,
"audioLevel" : 0.024540713
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [PITCH] Pitch detection analysis
DATA: {
"timestamp" : "2025-07-19T12:02:45Z",
"downsampledSamples" : 300,
"correlation" : 0.61790115,
"processingTimeMs" : 2.032041549682617,
"movingAveAmplitude" : 0.6680698,
"runLength" : 3,
"searchStrategy" : "economical",
"targetMaxFreq" : 9.566327,
"economicalSearchUsed" : true,
"bestPeriod" : 128,
"detectedPitch" : 7.6530614,
"movingAvePeriod" : 104.75335,
"audioLevel" : 0.00030351657,
"audioVariance" : 1.6503282e-09,
"audioSamples" : 300,
"targetMinFreq" : 7
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [AUDIO] Audio processing pipeline
DATA: {
"inputSamples" : 4800,
"filteredSamples" : 4800,
"maxAudioLevel" : 0.013317687,
"minAudioLevel" : 4.48199e-08,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"downsampling" : 1.2929439544677734,
"filtering" : 237.08093166351318,
"squaring" : 0.024080276489257812,
"autocorrelation" : 2.1419525146484375,
"smoothing" : 0.1220703125
},
"squaredSamples" : 4800,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:45Z",
"averageAudioLevel" : 0.0027668441
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [PRESSURE] Pressure calculation
DATA: {
"detectedPitch" : 7.6530614,
"audioLevel" : 0.013317687,
"calculatedPressure" : 3.9047751,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"currentState" : "active",
"hasOscillation" : true,
"breathDuration" : 2.738878011703491,
"audioLevel" : 0.013317687,
"timeSinceBreathStart" : 2.738878011703491,
"frequency" : 7.6530614,
"amplitudeHistory" : [
1.0682217,
1.0682217,
1.1222931,
1.150951,
0.83123666,
0.84936106,
0.31399444,
0.029986016,
0.23885527,
0.26031834
],
"isBreathing" : true,
"timeSinceLastOscillation" : 0,
"pressure" : 3.9047751,
"timestamp" : "2025-07-19T12:02:45Z",
"breathCount" : 3
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [PITCH] Pitch detection analysis
DATA: {
"movingAveAmplitude" : 0.6680698,
"searchStrategy" : "fallback",
"targetMinFreq" : 7,
"processingTimeMs" : 5.482912063598633,
"runLength" : 3,
"targetMaxFreq" : 9.566327,
"correlation" : 0.5150775,
"bestPeriod" : 109,
"audioLevel" : 0.00014557855,
"audioSamples" : 300,
"movingAvePeriod" : 104.75335,
"timestamp" : "2025-07-19T12:02:45Z",
"audioVariance" : 9.217451e-10,
"detectedPitch" : 0,
"downsampledSamples" : 300,
"economicalSearchUsed" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.020953733,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:45Z",
"downsampledSamples" : 97,
"minAudioLevel" : 1.55413e-06,
"averageAudioLevel" : 0.0029256314,
"processingStepTimeMs" : {
"filtering" : 237.24699020385742,
"autocorrelation" : 5.574941635131836,
"downsampling" : 1.2760162353515625,
"smoothing" : 0.12195110321044922,
"squaring" : 0.011920928955078125
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 0,
"audioLevel" : 0.020953733,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:45Z] [BREATH] Breath detection analysis
DATA: {
"currentState" : "active",
"isBreathing" : true,
"timeSinceLastOscillation" : 0,
"timeSinceBreathStart" : 2.989793062210083,
"audioLevel" : 0.020953733,
"breathCount" : 3,
"breathDuration" : 2.989793062210083,
"frequency" : 0,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"hasOscillation" : true,
"pressure" : 0,
"timestamp" : "2025-07-19T12:02:45Z",
"amplitudeHistory" : [
1.0682217,
1.1222931,
1.150951,
0.83123666,
0.84936106,
0.31399444,
0.029986016,
0.23885527,
0.26031834,
0.020953733
]
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [PITCH] Pitch detection analysis
DATA: {
"audioVariance" : 4.4977752e-10,
"processingTimeMs" : 5.338072776794434,
"audioLevel" : 0.00013764268,
"targetMinFreq" : 7,
"economicalSearchUsed" : false,
"downsampledSamples" : 300,
"targetMaxFreq" : 9.566327,
"runLength" : 3,
"correlation" : 0.3807611,
"movingAveAmplitude" : 0.6680698,
"audioSamples" : 300,
"timestamp" : "2025-07-19T12:02:46Z",
"detectedPitch" : 0,
"searchStrategy" : "fallback",
"bestPeriod" : 69,
"movingAvePeriod" : 104.75335
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"downsampling" : 1.278996467590332,
"filtering" : 236.8929386138916,
"squaring" : 0.02300739288330078,
"smoothing" : 0.12600421905517578,
"autocorrelation" : 5.4351091384887695
},
"minAudioLevel" : 1.17785476e-07,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:46Z",
"maxAudioLevel" : 0.010698136,
"squaredSamples" : 4800,
"smoothedSamples" : 97,
"averageAudioLevel" : 0.0013287787,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.010698136
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [BREATH] Breath detection analysis
DATA: {
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)",
"breathDuration" : 3.240262031555176,
"timeSinceBreathStart" : 3.240262031555176,
"currentState" : "active",
"breathCount" : 3,
"timeSinceLastOscillation" : 0,
"audioLevel" : 0.010698136,
"pressure" : 0,
"timestamp" : "2025-07-19T12:02:46Z",
"amplitudeHistory" : [
1.1222931,
1.150951,
0.83123666,
0.84936106,
0.31399444,
0.029986016,
0.23885527,
0.26031834,
0.020953733,
0.010698136
],
"frequency" : 0,
"hasOscillation" : true,
"isBreathing" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [PITCH] Pitch detection analysis
DATA: {
"downsampledSamples" : 300,
"detectedPitch" : 0,
"bestPeriod" : 116,
"correlation" : 0.5669563,
"audioSamples" : 300,
"audioVariance" : 4.124816e-10,
"economicalSearchUsed" : false,
"movingAvePeriod" : 104.75335,
"processingTimeMs" : 5.282998085021973,
"targetMaxFreq" : 9.566327,
"audioLevel" : 0.00013764268,
"movingAveAmplitude" : 0.6680698,
"timestamp" : "2025-07-19T12:02:46Z",
"targetMinFreq" : 7,
"searchStrategy" : "fallback",
"runLength" : 3
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0053550405,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:46Z",
"downsampledSamples" : 97,
"minAudioLevel" : 3.635705e-07,
"averageAudioLevel" : 0.0008436609,
"processingStepTimeMs" : {
"squaring" : 0.012993812561035156,
"filtering" : 237.18202114105225,
"downsampling" : 1.267075538635254,
"autocorrelation" : 5.386114120483398,
"smoothing" : 0.12302398681640625
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0053550405,
"detectedPitch" : 0,
"calculatedPressure" : 0,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : true,
"amplitudeHistory" : [
1.150951,
0.83123666,
0.84936106,
0.31399444,
0.029986016,
0.23885527,
0.26031834,
0.020953733,
0.010698136,
0.0053550405
],
"breathCount" : 3,
"audioLevel" : 0.0053550405,
"frequency" : 0,
"currentState" : "active",
"timeSinceBreathStart" : 3.490893006324768,
"timestamp" : "2025-07-19T12:02:46Z",
"hasOscillation" : true,
"breathDuration" : 3.490893006324768,
"timeSinceLastOscillation" : 0,
"pressure" : 0,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0020247682,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:46Z",
"downsampledSamples" : 97,
"minAudioLevel" : 9.978976e-09,
"averageAudioLevel" : 0.000472437,
"processingStepTimeMs" : {
"downsampling" : 1.289963722229004,
"squaring" : 0.02396106719970703,
"filtering" : 237.4579906463623,
"smoothing" : 0.13303756713867188,
"autocorrelation" : 0.1919269561767578
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"audioLevel" : 0.0020247682,
"frameCount" : 4800,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : true,
"amplitudeHistory" : [
1.150951,
0.83123666,
0.84936106,
0.31399444,
0.029986016,
0.23885527,
0.26031834,
0.020953733,
0.010698136,
0.0053550405
],
"breathCount" : 3,
"audioLevel" : 0.0020247682,
"frequency" : 0,
"currentState" : "active",
"timeSinceBreathStart" : 3.736667037010193,
"timestamp" : "2025-07-19T12:02:46Z",
"hasOscillation" : false,
"breathDuration" : 3.736667037010193,
"timeSinceLastOscillation" : 0.2457740306854248,
"pressure" : 0,
"stateTransitionReason" : "Breath duration (1.7s) >= minimum (1.5s)"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [AUDIO] Audio processing pipeline
DATA: {
"inputSamples" : 4800,
"filteredSamples" : 4800,
"maxAudioLevel" : 0.0021026586,
"minAudioLevel" : 1.0179137e-06,
"smoothedSamples" : 97,
"processingStepTimeMs" : {
"smoothing" : 0.12099742889404297,
"downsampling" : 1.2700557708740234,
"filtering" : 237.29395866394043,
"squaring" : 0.024080276489257812,
"autocorrelation" : 0.19109249114990234
},
"squaredSamples" : 4800,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:46Z",
"averageAudioLevel" : 0.0004951493
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0,
"audioLevel" : 0.0021026586
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:46Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : true,
"amplitudeHistory" : [
1.150951,
0.83123666,
0.84936106,
0.31399444,
0.029986016,
0.23885527,
0.26031834,
0.020953733,
0.010698136,
0.0053550405
],
"breathCount" : 3,
"audioLevel" : 0.0021026586,
"frequency" : 0,
"currentState" : "ending",
"timeSinceBreathStart" : 3.982185959815979,
"timestamp" : "2025-07-19T12:02:46Z",
"hasOscillation" : false,
"breathDuration" : 3.982185959815979,
"timeSinceLastOscillation" : 0.49129295349121094,
"pressure" : 0,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.0019783948,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:47Z",
"downsampledSamples" : 97,
"minAudioLevel" : 1.3410681e-07,
"averageAudioLevel" : 0.00043027196,
"processingStepTimeMs" : {
"smoothing" : 0.1289844512939453,
"filtering" : 236.8459701538086,
"squaring" : 0.012040138244628906,
"autocorrelation" : 0.19109249114990234,
"downsampling" : 1.2969970703125
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"frameCount" : 4800,
"detectedPitch" : 0,
"audioLevel" : 0.0019783948
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : false,
"amplitudeHistory" : [

],
"breathCount" : 4,
"audioLevel" : 0.0019783948,
"frequency" : 0,
"currentState" : "completed",
"timeSinceBreathStart" : 4.22727906703949,
"timestamp" : "2025-07-19T12:02:47Z",
"hasOscillation" : false,
"breathDuration" : 0,
"timeSinceLastOscillation" : 0.7363860607147217,
"pressure" : 0,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)"
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"squaring" : 0.025033950805664062,
"filtering" : 236.64391040802002,
"autocorrelation" : 0.24199485778808594,
"downsampling" : 1.2840032577514648,
"smoothing" : 0.12302398681640625
},
"minAudioLevel" : 6.3831976e-08,
"timestamp" : "2025-07-19T12:02:47Z",
"downsampledSamples" : 97,
"maxAudioLevel" : 0.0018211665,
"squaredSamples" : 4800,
"smoothedSamples" : 97,
"averageAudioLevel" : 0.00037425297,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.0018211665,
"frameCount" : 4800,
"detectedPitch" : 0,
"calculatedPressure" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : false,
"breathCount" : 4,
"amplitudeHistory" : [

],
"pressure" : 0,
"breathDuration" : 0,
"currentState" : "idle",
"frequency" : 0,
"audioLevel" : 0.0018211665,
"stateTransitionReason" : "Silence gap (0.5s) > max (0.3s)",
"timestamp" : "2025-07-19T12:02:47Z",
"hasOscillation" : false
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [PITCH] Pitch detection analysis
DATA: {
"economicalSearchUsed" : false,
"processingTimeMs" : 5.414009094238281,
"movingAveAmplitude" : 0.7675183,
"runLength" : 2,
"audioSamples" : 300,
"detectedPitch" : 13.419066,
"downsampledSamples" : 300,
"movingAvePeriod" : 88.87668,
"timestamp" : "2025-07-19T12:02:47Z",
"targetMinFreq" : 10.0643,
"targetMaxFreq" : 16.773832,
"audioLevel" : 0.00018207476,
"audioVariance" : 2.6332525e-10,
"searchStrategy" : "fallback",
"correlation" : 0.8669667,
"bestPeriod" : 73
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"smoothing" : 0.12195110321044922,
"filtering" : 236.94097995758057,
"autocorrelation" : 5.547046661376953,
"squaring" : 0.024080276489257812,
"downsampling" : 1.2880563735961914
},
"minAudioLevel" : 6.984483e-07,
"timestamp" : "2025-07-19T12:02:47Z",
"downsampledSamples" : 97,
"maxAudioLevel" : 0.019585023,
"squaredSamples" : 4800,
"smoothedSamples" : 97,
"averageAudioLevel" : 0.0023389356,
"filteredSamples" : 4800,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.019585023,
"frameCount" : 4800,
"calculatedPressure" : 10.3569355,
"detectedPitch" : 13.419066
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [BREATH] Breath detection analysis
DATA: {
"isBreathing" : true,
"breathCount" : 4,
"amplitudeHistory" : [
0.005
],
"pressure" : 10.3569355,
"breathDuration" : 0,
"currentState" : "starting",
"audioLevel" : 0.019585023,
"frequency" : 13.419066,
"timeSinceLastOscillation" : 0,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"timestamp" : "2025-07-19T12:02:47Z",
"hasOscillation" : true
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 5.110025405883789,
"economicalSearchUsed" : false,
"correlation" : 0.36429682,
"audioLevel" : 0.0002114465,
"downsampledSamples" : 300,
"timestamp" : "2025-07-19T12:02:47Z",
"detectedPitch" : 0,
"targetMaxFreq" : 16.773832,
"searchStrategy" : "fallback",
"movingAvePeriod" : 88.87668,
"runLength" : 2,
"targetMinFreq" : 10.0643,
"audioVariance" : 8.753226e-10,
"audioSamples" : 300,
"bestPeriod" : 24,
"movingAveAmplitude" : 0.7675183
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"smoothing" : 0.12099742889404297,
"filtering" : 237.9549741744995,
"autocorrelation" : 5.203962326049805,
"squaring" : 0.012993812561035156,
"downsampling" : 1.2879371643066406
},
"minAudioLevel" : 1.3459066e-07,
"timestamp" : "2025-07-19T12:02:47Z",
"downsampledSamples" : 97,
"maxAudioLevel" : 0.024076238,
"squaredSamples" : 4800,
"smoothedSamples" : 97,
"averageAudioLevel" : 0.0037176025,
"filteredSamples" : 4800,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.024076238,
"frameCount" : 4800,
"calculatedPressure" : 0,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:47Z] [BREATH] Breath detection analysis
DATA: {
"breathCount" : 4,
"amplitudeHistory" : [
0.005,
0.024076238
],
"currentState" : "starting",
"timeSinceLastOscillation" : 0,
"hasOscillation" : true,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"timeSinceBreathStart" : 0.25124597549438477,
"timestamp" : "2025-07-19T12:02:47Z",
"isBreathing" : true,
"frequency" : 0,
"pressure" : 0,
"breathDuration" : 0.25124597549438477,
"audioLevel" : 0.024076238
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 5.380988121032715,
"economicalSearchUsed" : false,
"correlation" : 0.6360005,
"audioLevel" : 0.00028859862,
"downsampledSamples" : 300,
"detectedPitch" : 10.533246,
"timestamp" : "2025-07-19T12:02:48Z",
"searchStrategy" : "fallback",
"targetMaxFreq" : 13.166557,
"movingAvePeriod" : 90.251114,
"runLength" : 3,
"targetMinFreq" : 7.899935,
"bestPeriod" : 93,
"audioSamples" : 300,
"audioVariance" : 2.3446638e-09,
"movingAveAmplitude" : 0.723679
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.029270336,
"squaredSamples" : 4800,
"inputSamples" : 4800,
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:48Z",
"downsampledSamples" : 97,
"minAudioLevel" : 1.955952e-06,
"averageAudioLevel" : 0.0046236925,
"processingStepTimeMs" : {
"filtering" : 237.0070219039917,
"downsampling" : 1.2919902801513672,
"squaring" : 0.024080276489257812,
"autocorrelation" : 5.4939985275268555,
"smoothing" : 0.12195110321044922
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.029270336,
"frameCount" : 4800,
"detectedPitch" : 10.533246,
"calculatedPressure" : 7.127702
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [BREATH] Breath detection analysis
DATA: {
"breathCount" : 4,
"amplitudeHistory" : [
0.005,
0.024076238,
0.47518015
],
"currentState" : "starting",
"timeSinceLastOscillation" : 0,
"hasOscillation" : true,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"timeSinceBreathStart" : 0.5018429756164551,
"timestamp" : "2025-07-19T12:02:48Z",
"isBreathing" : true,
"frequency" : 10.533246,
"pressure" : 7.127702,
"breathDuration" : 0.5018429756164551,
"audioLevel" : 0.029270336
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 5.855083465576172,
"economicalSearchUsed" : false,
"correlation" : 0.5957062,
"audioLevel" : 0.000470998,
"downsampledSamples" : 300,
"timestamp" : "2025-07-19T12:02:48Z",
"detectedPitch" : 0,
"targetMaxFreq" : 13.166557,
"searchStrategy" : "fallback",
"movingAvePeriod" : 90.251114,
"runLength" : 3,
"targetMinFreq" : 7.899935,
"audioVariance" : 4.0114667e-09,
"audioSamples" : 300,
"bestPeriod" : 58,
"movingAveAmplitude" : 0.723679
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"autocorrelation" : 5.95402717590332,
"smoothing" : 0.12302398681640625,
"filtering" : 237.1230125427246,
"squaring" : 0.012993812561035156,
"downsampling" : 1.2700557708740234
},
"minAudioLevel" : 2.7023598e-06,
"timestamp" : "2025-07-19T12:02:48Z",
"downsampledSamples" : 97,
"maxAudioLevel" : 0.038505234,
"squaredSamples" : 4800,
"smoothedSamples" : 97,
"averageAudioLevel" : 0.004939059,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"frameCount" : 4800,
"audioLevel" : 0.038505234,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [BREATH] Breath detection analysis
DATA: {
"breathCount" : 4,
"amplitudeHistory" : [
0.005,
0.024076238,
0.47518015,
0.038505234
],
"currentState" : "starting",
"timeSinceLastOscillation" : 0,
"hasOscillation" : true,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"timeSinceBreathStart" : 0.7530349493026733,
"isBreathing" : true,
"pressure" : 0,
"timestamp" : "2025-07-19T12:02:48Z",
"frequency" : 0,
"breathDuration" : 0.7530349493026733,
"audioLevel" : 0.038505234
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 5.432009696960449,
"economicalSearchUsed" : false,
"correlation" : 0.72461396,
"audioLevel" : 0.00073077553,
"downsampledSamples" : 300,
"detectedPitch" : 7.256236,
"timestamp" : "2025-07-19T12:02:48Z",
"searchStrategy" : "fallback",
"targetMaxFreq" : 9.070295,
"movingAvePeriod" : 105.167404,
"runLength" : 3,
"targetMinFreq" : 7,
"bestPeriod" : 135,
"audioSamples" : 300,
"audioVariance" : 8.745386e-09,
"movingAveAmplitude" : 0.7239907
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [AUDIO] Audio processing pipeline
DATA: {
"maxAudioLevel" : 0.041046564,
"squaredSamples" : 4800,
"filteredSamples" : 4800,
"inputSamples" : 4800,
"timestamp" : "2025-07-19T12:02:48Z",
"downsampledSamples" : 97,
"minAudioLevel" : 1.9566432e-06,
"averageAudioLevel" : 0.0059423046,
"processingStepTimeMs" : {
"squaring" : 0.02300739288330078,
"autocorrelation" : 5.560040473937988,
"filtering" : 237.28597164154053,
"downsampling" : 1.2780427932739258,
"smoothing" : 0.1220703125
},
"smoothedSamples" : 97
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.041046564,
"frameCount" : 4800,
"detectedPitch" : 7.256236,
"calculatedPressure" : 3.4607282
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [BREATH] Breath detection analysis
DATA: {
"breathCount" : 4,
"amplitudeHistory" : [
0.005,
0.024076238,
0.47518015,
0.038505234,
0.23071522
],
"currentState" : "starting",
"timeSinceLastOscillation" : 0,
"hasOscillation" : true,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"timeSinceBreathStart" : 1.003991961479187,
"timestamp" : "2025-07-19T12:02:48Z",
"isBreathing" : true,
"frequency" : 7.256236,
"pressure" : 3.4607282,
"breathDuration" : 1.003991961479187,
"audioLevel" : 0.041046564
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 1.896977424621582,
"economicalSearchUsed" : true,
"correlation" : 0.72706187,
"audioLevel" : 0.00073077553,
"downsampledSamples" : 300,
"timestamp" : "2025-07-19T12:02:48Z",
"detectedPitch" : 7.256236,
"targetMaxFreq" : 9.070295,
"searchStrategy" : "economical",
"movingAvePeriod" : 115.1116,
"runLength" : 3,
"targetMinFreq" : 7,
"audioVariance" : 8.541874e-09,
"audioSamples" : 300,
"bestPeriod" : 135,
"movingAveAmplitude" : 0.7250144
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"squaring" : 0.024080276489257812,
"autocorrelation" : 2.005934715270996,
"downsampling" : 1.2720823287963867,
"smoothing" : 0.12099742889404297,
"filtering" : 237.59102821350098
},
"minAudioLevel" : 1.5285477e-06,
"timestamp" : "2025-07-19T12:02:48Z",
"downsampledSamples" : 97,
"maxAudioLevel" : 0.030975338,
"squaredSamples" : 4800,
"smoothedSamples" : 97,
"averageAudioLevel" : 0.004332178,
"filteredSamples" : 4800,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.030975338,
"calculatedPressure" : 3.4607282,
"frameCount" : 4800,
"detectedPitch" : 7.256236
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:48Z] [BREATH] Breath detection analysis
DATA: {
"breathCount" : 4,
"amplitudeHistory" : [
0.005,
0.024076238,
0.47518015,
0.038505234,
0.23071522,
0.23071522
],
"currentState" : "starting",
"timeSinceLastOscillation" : 0,
"hasOscillation" : true,
"stateTransitionReason" : "Oscillation detected, starting new breath",
"timeSinceBreathStart" : 1.2516900300979614,
"isBreathing" : true,
"pressure" : 3.4607282,
"timestamp" : "2025-07-19T12:02:48Z",
"frequency" : 7.256236,
"breathDuration" : 1.2516900300979614,
"audioLevel" : 0.030975338
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 5.13303279876709,
"economicalSearchUsed" : false,
"correlation" : 0.49151692,
"audioLevel" : 0.00073077553,
"downsampledSamples" : 300,
"timestamp" : "2025-07-19T12:02:49Z",
"detectedPitch" : 0,
"targetMaxFreq" : 9.070295,
"searchStrategy" : "fallback",
"movingAvePeriod" : 115.1116,
"runLength" : 3,
"targetMinFreq" : 7,
"audioVariance" : 7.506805e-09,
"audioSamples" : 300,
"bestPeriod" : 111,
"movingAveAmplitude" : 0.7250144
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"autocorrelation" : 5.225062370300293,
"squaring" : 0.012993812561035156,
"smoothing" : 0.12195110321044922,
"filtering" : 237.48493194580078,
"downsampling" : 1.2990236282348633
},
"minAudioLevel" : 1.0403528e-07,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:49Z",
"maxAudioLevel" : 0.0322593,
"squaredSamples" : 4800,
"smoothedSamples" : 97,
"averageAudioLevel" : 0.004268517,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [PRESSURE] Pressure calculation
DATA: {
"frameCount" : 4800,
"calculatedPressure" : 0,
"audioLevel" : 0.0322593,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [BREATH] Breath detection analysis
DATA: {
"breathCount" : 4,
"amplitudeHistory" : [
0.005,
0.024076238,
0.47518015,
0.038505234,
0.23071522,
0.23071522,
0.0322593
],
"currentState" : "active",
"timeSinceLastOscillation" : 0,
"hasOscillation" : true,
"stateTransitionReason" : "Breath duration (1.5s) >= minimum (1.5s)",
"timeSinceBreathStart" : 1.5024620294570923,
"frequency" : 0,
"isBreathing" : true,
"breathDuration" : 1.5024620294570923,
"timestamp" : "2025-07-19T12:02:49Z",
"pressure" : 0,
"audioLevel" : 0.0322593
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 5.137920379638672,
"economicalSearchUsed" : false,
"correlation" : 0.56159586,
"audioLevel" : 0.0002949942,
"downsampledSamples" : 300,
"timestamp" : "2025-07-19T12:02:49Z",
"detectedPitch" : 0,
"targetMaxFreq" : 9.070295,
"searchStrategy" : "fallback",
"movingAvePeriod" : 115.1116,
"runLength" : 3,
"targetMinFreq" : 7,
"audioVariance" : 3.3848455e-09,
"audioSamples" : 300,
"bestPeriod" : 97,
"movingAveAmplitude" : 0.7250144
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"filtering" : 236.97197437286377,
"smoothing" : 0.1220703125,
"downsampling" : 1.2919902801513672,
"squaring" : 0.012040138244628906,
"autocorrelation" : 5.236983299255371
},
"minAudioLevel" : 6.502378e-07,
"timestamp" : "2025-07-19T12:02:49Z",
"downsampledSamples" : 97,
"maxAudioLevel" : 0.026431045,
"squaredSamples" : 4800,
"smoothedSamples" : 97,
"averageAudioLevel" : 0.005170217,
"filteredSamples" : 4800,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"audioLevel" : 0.026431045,
"detectedPitch" : 0,
"frameCount" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [BREATH] Breath detection analysis
DATA: {
"breathCount" : 4,
"amplitudeHistory" : [
0.005,
0.024076238,
0.47518015,
0.038505234,
0.23071522,
0.23071522,
0.0322593,
0.026431045
],
"currentState" : "active",
"timeSinceLastOscillation" : 0,
"hasOscillation" : true,
"stateTransitionReason" : "Breath duration (1.5s) >= minimum (1.5s)",
"pressure" : 0,
"frequency" : 0,
"timeSinceBreathStart" : 1.7529040575027466,
"breathDuration" : 1.7529040575027466,
"isBreathing" : true,
"timestamp" : "2025-07-19T12:02:49Z",
"audioLevel" : 0.026431045
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [PITCH] Pitch detection analysis
DATA: {
"processingTimeMs" : 5.234956741333008,
"economicalSearchUsed" : false,
"correlation" : 0.630612,
"audioLevel" : 0.00036570657,
"downsampledSamples" : 300,
"timestamp" : "2025-07-19T12:02:49Z",
"detectedPitch" : 10.204082,
"targetMaxFreq" : 12.755102,
"searchStrategy" : "fallback",
"movingAvePeriod" : 110.3337,
"runLength" : 4,
"targetMinFreq" : 7.653061,
"audioVariance" : 4.385898e-09,
"audioSamples" : 300,
"bestPeriod" : 96,
"movingAveAmplitude" : 0.70141375
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [AUDIO] Audio processing pipeline
DATA: {
"processingStepTimeMs" : {
"squaring" : 0.026941299438476562,
"filtering" : 237.68198490142822,
"autocorrelation" : 5.375981330871582,
"smoothing" : 0.13697147369384766,
"downsampling" : 1.2739896774291992
},
"minAudioLevel" : 1.05836534e-07,
"downsampledSamples" : 97,
"timestamp" : "2025-07-19T12:02:49Z",
"maxAudioLevel" : 0.02970762,
"squaredSamples" : 4800,
"smoothedSamples" : 97,
"averageAudioLevel" : 0.0046934625,
"inputSamples" : 4800,
"filteredSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [PRESSURE] Pressure calculation
DATA: {
"audioLevel" : 0.02970762,
"calculatedPressure" : 6.7593665,
"frameCount" : 4800,
"detectedPitch" : 10.204082
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [PITCH] Pitch detection analysis
DATA: {
"economicalSearchUsed" : false,
"processingTimeMs" : 5.903005599975586,
"movingAveAmplitude" : 0.70141375,
"runLength" : 4,
"audioSamples" : 300,
"detectedPitch" : 0,
"downsampledSamples" : 300,
"movingAvePeriod" : 110.3337,
"timestamp" : "2025-07-19T12:02:49Z",
"targetMinFreq" : 7.653061,
"targetMaxFreq" : 12.755102,
"audioVariance" : 3.5050054e-09,
"searchStrategy" : "fallback",
"bestPeriod" : 97,
"correlation" : 0.50221443,
"audioLevel" : 0.00036570657
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [AUDIO] Audio processing pipeline
DATA: {
"smoothedSamples" : 97,
"downsampledSamples" : 97,
"maxAudioLevel" : 0.01972057,
"squaredSamples" : 4800,
"minAudioLevel" : 3.3847755e-07,
"processingStepTimeMs" : {
"smoothing" : 0.1239776611328125,
"squaring" : 0.026941299438476562,
"filtering" : 236.95898056030273,
"downsampling" : 1.2819766998291016,
"autocorrelation" : 6.006002426147461
},
"filteredSamples" : 4800,
"timestamp" : "2025-07-19T12:02:49Z",
"averageAudioLevel" : 0.0028142943,
"inputSamples" : 4800
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:49Z] [PRESSURE] Pressure calculation
DATA: {
"calculatedPressure" : 0,
"frameCount" : 4800,
"audioLevel" : 0.01972057,
"detectedPitch" : 0
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:50Z] [BREATH] Breath detection analysis
DATA: {
"audioLevel" : 0.02970762,
"timeSinceLastOscillation" : 0,
"frequency" : 10.204082,
"isBreathing" : true,
"hasOscillation" : true,
"amplitudeHistory" : [
0.005,
0.024076238,
0.47518015,
0.038505234,
0.23071522,
0.23071522,
0.0322593,
0.026431045,
0.45062444
],
"timestamp" : "2025-07-19T12:02:50Z",
"currentState" : "active",
"pressure" : 6.7593665,
"stateTransitionReason" : "Breath duration (1.5s) >= minimum (1.5s)",
"breathDuration" : 2.370903968811035,
"timeSinceBreathStart" : 2.370903968811035,
"breathCount" : 4
}
--------------------------------------------------------------------------------
[2025-07-19T12:02:50Z] [BREATH] Breath detection analysis
DATA: {
"frequency" : 0,
"timeSinceBreathStart" : 2.3710349798202515,
"stateTransitionReason" : "Breath duration (1.5s) >= minimum (1.5s)",
"timestamp" : "2025-07-19T12:02:50Z",
"isBreathing" : true,
"pressure" : 0,
"hasOscillation" : true,
"currentState" : "active",
"audioLevel" : 0.01972057,
"amplitudeHistory" : [
0.005,
0.024076238,
0.47518015,
0.038505234,
0.23071522,
0.23071522,
0.0322593,
0.026431045,
0.45062444,
0.01972057
],
"timeSinceLastOscillation" : 0,
"breathDuration" : 2.3710349798202515,
"breathCount" : 4
}
--------------------------------------------------------------------------------

================================================================================
DEBUG LOG SESSION ENDED
================================================================================
End Time: 2025-07-19 12:02:57 pm +0000
Total Log Entries: 467
================================================================================

Sent from my iPhone