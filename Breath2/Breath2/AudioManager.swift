//
//  AudioManager.swift
//  Breath2
//
//  Created by Mo on 02/06/2025.
//

import Foundation
import AVFoundation
import Combine

class AudioManager: ObservableObject {
    // MARK: - Published Properties
    @Published var currentFrequency: Double = 0.0
    @Published var currentPressure: Double = 0.0
    @Published var audioLevel: Float = 0.0
    @Published var hasPermission: Bool = false

    // Breath detection
    @Published var breathDetector = BreathDetector()

    // MARK: - Private Properties
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var pitchDetector: PitchDetector?
    private var pressureCalculator: PressureCalculator?

    // Configuration management
    private var configurationManager: ConfigurationManager?
    private var sampleRate: Double = 44100.0
    private var bufferSize: AVAudioFrameCount = 4410 // Default buffer size

    // MARK: - Pipeline State Management (Paper Specification)
    private var totalSamplesProcessed: Int = 0  // Track absolute sample position across chunks

    // MARK: - Initialization
    init(configurationManager: ConfigurationManager? = nil) {
        self.configurationManager = configurationManager
        setupAudioComponents()

        // Listen for configuration changes if manager is provided
        if let configManager = configurationManager {
            setupConfigurationObserver(configManager)
        }
    }

    convenience init() {
        self.init(configurationManager: nil)
    }

    /// **Cleans up** audio resources when the manager is deallocated.
    deinit {
        stopRecording()
        audioEngine = nil
        inputNode = nil
        pitchDetector = nil
        pressureCalculator = nil
        print("🧹 AudioManager deallocated - resources cleaned up")
    }
    
    // MARK: - Public Methods
    func requestPermission() {
        #if os(iOS)
        if #available(iOS 17.0, *) {
            AVAudioApplication.requestRecordPermission { [weak self] granted in
                DispatchQueue.main.async {
                    self?.hasPermission = granted
                    if granted {
                        self?.setupAudioSession()
                    }
                }
            }
        } else {
            AVAudioSession.sharedInstance().requestRecordPermission { [weak self] granted in
                DispatchQueue.main.async {
                    self?.hasPermission = granted
                    if granted {
                        self?.setupAudioSession()
                    }
                }
            }
        }
        #endif
    }
    
    func startRecording() {
        print("🎬 startRecording called")
        guard hasPermission else {
            print("❌ Audio permission not granted")
            return
        }

        // Reset breath detection for new session
        breathDetector.reset()

        // Reset pipeline state for new session (Paper specification)
        totalSamplesProcessed = 0

        print("🔧 Setting up audio engine...")
        setupAudioEngine()

        // Start new session as specified in paper
        pitchDetector?.startNewSession()

        do {
            try audioEngine?.start()
            print("✅ Audio engine started successfully")
        } catch {
            print("❌ Failed to start audio engine: \(error)")
        }
    }

    func stopRecording() {
        print("🛑 stopRecording called")
        audioEngine?.stop()
        if let inputNode = audioEngine?.inputNode {
            inputNode.removeTap(onBus: 0)
        }
        print("✅ Audio engine stopped")
    }
    
    // MARK: - Configuration Management

    /// Update configuration at runtime
    func updateConfiguration(_ configuration: AlgorithmConfiguration) {
        // Update pitch detector configuration
        pitchDetector?.updateConfiguration(configuration)

        // Update buffer size if needed
        let newBufferSize = AVAudioFrameCount(sampleRate * Double(configuration.bufferSize))
        if newBufferSize != bufferSize {
            bufferSize = newBufferSize
            // Restart audio engine if running to apply new buffer size
            if audioEngine?.isRunning == true {
                restartAudioEngine()
            }
        }

        print("🔄 AudioManager configuration updated")
    }

    /// Setup configuration observer
    private func setupConfigurationObserver(_ configManager: ConfigurationManager) {
        // Observe configuration changes
        configManager.$currentConfiguration
            .sink { [weak self] newConfiguration in
                self?.updateConfiguration(newConfiguration)
            }
            .store(in: &cancellables)
    }

    private var cancellables = Set<AnyCancellable>()

    // MARK: - Private Methods
    private func setupAudioComponents() {
        let config = configurationManager?.currentConfiguration ?? AlgorithmConfiguration.standard

        // Create pitch detector with configuration
        pitchDetector = PitchDetector(configuration: config)
        pressureCalculator = PressureCalculator()

        // Set buffer size from configuration
        bufferSize = AVAudioFrameCount(sampleRate * Double(config.bufferSize))
    }

    private func restartAudioEngine() {
        let wasRunning = audioEngine?.isRunning == true
        if wasRunning {
            stopRecording()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.startRecording()
            }
        }
    }
    
    private func setupAudioSession() {
        #if os(iOS)
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .measurement, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true, options: [])
            print("✅ Audio session configured successfully")
        } catch {
            print("❌ Failed to setup audio session: \(error)")
        }
        #endif
    }
    
    private func setupAudioEngine() {
        audioEngine = AVAudioEngine()
        inputNode = audioEngine?.inputNode

        guard let inputNode = inputNode else {
            print("Failed to get input node")
            return
        }

        let inputFormat = inputNode.outputFormat(forBus: 0)
        print("Input format: \(inputFormat)")

        // Update sample rate based on actual input format
        sampleRate = inputFormat.sampleRate

        // Calculate buffer size for exactly 0.1sec as per paper
        bufferSize = AVAudioFrameCount(sampleRate * 0.1)

        // Recreate pitch detector with correct sample rate and current configuration
        let config = configurationManager?.currentConfiguration ?? AlgorithmConfiguration.standard
        var updatedConfig = config
        updatedConfig.sampleRate = Float(sampleRate)

        pitchDetector = PitchDetector(configuration: updatedConfig)

        // Install tap to process audio data
        inputNode.installTap(onBus: 0, bufferSize: bufferSize, format: inputFormat) { [weak self] buffer, time in
            self?.processAudioBuffer(buffer)
        }

        audioEngine?.prepare()
    }
    
    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
        print("📊 processAudioBuffer called - frameLength: \(buffer.frameLength)")

        guard let channelData = buffer.floatChannelData?[0] else {
            print("❌ No channel data available")
            return
        }

        guard let pitchDetector = pitchDetector else {
            print("❌ PitchDetector is nil")
            return
        }

        guard let pressureCalculator = pressureCalculator else {
            print("❌ PressureCalculator is nil")
            return
        }

        let frameCount = Int(buffer.frameLength)
        let audioData = Array(UnsafeBufferPointer(start: channelData, count: frameCount))

        // Debug: Check if we're getting audio data
        let audioLevel = audioData.map { abs($0) }.max() ?? 0.0
        print("🎤 Audio Level: \(audioLevel), Samples: \(frameCount)")

        // Process audio chunk through pitch detector
        print("🔍 Processing audio through pitch detector...")
        let detectedPitch = pitchDetector.processChunk(audioData)
        print("🎵 Detected pitch: \(detectedPitch) Hz")

        // Convert pitch to pressure using the linear model from the paper
        let calculatedPressure = pressureCalculator.calculatePressure(fromPitch: detectedPitch)
        print("� Calculated pressure: \(calculatedPressure) cm H2O")

        // Log pressure calculation for debugging
        DebugLogger.shared.log(.pressureCalculation, "Pressure calculation", data: [
            "detectedPitch": detectedPitch,
            "calculatedPressure": calculatedPressure,
            "audioLevel": audioLevel,
            "frameCount": Float(frameCount)
        ])

        // Update UI on main thread
        DispatchQueue.main.async { [weak self] in
            self?.currentFrequency = Double(detectedPitch)
            self?.currentPressure = Double(calculatedPressure)
            self?.audioLevel = audioLevel

            // Process breath detection using pressure and frequency signals
            self?.breathDetector.processBreathingSignals(
                pressure: calculatedPressure,
                frequency: detectedPitch,
                audioLevel: audioLevel
            )

            print("🖥️ UI updated - Freq: \(detectedPitch), Pressure: \(calculatedPressure), Breaths: \(self?.breathDetector.breathCount ?? 0)")
        }
    }


}
