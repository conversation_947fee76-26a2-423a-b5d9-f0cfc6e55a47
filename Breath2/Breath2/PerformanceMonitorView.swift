//
//  PerformanceMonitorView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Real-time performance monitoring for algorithm parameters

import SwiftUI

struct PerformanceMonitorView: View {
    @ObservedObject var configManager: ConfigurationManager
    @State private var refreshTimer: Timer?
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                headerSection
                performanceMetricsSection
                systemResourcesSection
                algorithmStatusSection
                recommendationsSection
            }
            .padding()
        }
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.05, green: 0.05, blue: 0.08),
                    Color(red: 0.08, green: 0.08, blue: 0.12),
                    Color(red: 0.10, green: 0.10, blue: 0.15)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .onAppear {
            startPerformanceMonitoring()
        }
        .onDisappear {
            stopPerformanceMonitoring()
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "speedometer")
                    .font(.title2)
                    .foregroundColor(.cyan)
                
                Text("Performance Monitor")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button("Reset Metrics") {
                    configManager.resetPerformanceMetrics()
                }
                .buttonStyle(.bordered)
                .foregroundColor(.cyan)
            }
            
            Text("Real-time monitoring of algorithm performance and system resources")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
    }
    
    // MARK: - Performance Metrics Section
    
    private var performanceMetricsSection: some View {
        ParameterSection(title: "Algorithm Performance", icon: "chart.line.uptrend.xyaxis") {
            let metrics = configManager.performanceMetrics
            
            VStack(spacing: 16) {
                PerformanceMeter(
                    title: "Processing Time",
                    value: metrics.processingTime * 1000, // Convert to ms
                    range: 0...100,
                    unit: "ms",
                    warningThreshold: 50,
                    errorThreshold: 80
                )
                
                PerformanceMeter(
                    title: "Average Latency",
                    value: metrics.averageLatency * 1000, // Convert to ms
                    range: 0...500,
                    unit: "ms",
                    warningThreshold: 200,
                    errorThreshold: 300
                )
                
                PerformanceMeter(
                    title: "Detection Rate",
                    value: metrics.detectionRate * 100, // Convert to percentage
                    range: 0...100,
                    unit: "%",
                    warningThreshold: 70,
                    errorThreshold: 50
                )
                
                StatusIndicator(
                    title: "Samples Processed",
                    value: "\(metrics.samplesProcessed)",
                    status: .neutral,
                    icon: "waveform"
                )
                
                StatusIndicator(
                    title: "Successful Detections",
                    value: "\(metrics.successfulDetections)",
                    status: metrics.detectionRate > 0.8 ? .good : .warning,
                    icon: "checkmark.circle"
                )
            }
        }
    }
    
    // MARK: - System Resources Section
    
    private var systemResourcesSection: some View {
        ParameterSection(title: "System Resources", icon: "cpu") {
            let metrics = configManager.performanceMetrics
            
            VStack(spacing: 16) {
                PerformanceMeter(
                    title: "CPU Usage",
                    value: metrics.cpuUsage * 100, // Convert to percentage
                    range: 0...100,
                    unit: "%",
                    warningThreshold: 50,
                    errorThreshold: 80
                )
                
                PerformanceMeter(
                    title: "Memory Usage",
                    value: Double(metrics.memoryUsage) / 1_000_000, // Convert to MB
                    range: 0...100,
                    unit: "MB",
                    warningThreshold: 50,
                    errorThreshold: 80
                )
                
                StatusIndicator(
                    title: "Buffer Overruns",
                    value: "\(metrics.bufferOverruns)",
                    status: metrics.bufferOverruns == 0 ? .good : .warning,
                    icon: "exclamationmark.triangle"
                )
                
                StatusIndicator(
                    title: "Dropped Samples",
                    value: "\(metrics.droppedSamples)",
                    status: metrics.droppedSamples == 0 ? .good : .error,
                    icon: "xmark.circle"
                )
            }
        }
    }
    
    // MARK: - Algorithm Status Section
    
    private var algorithmStatusSection: some View {
        ParameterSection(title: "Algorithm Status", icon: "gear.badge.checkmark") {
            VStack(spacing: 16) {
                HStack {
                    Text("Overall Status")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Text(configManager.performanceStatus)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(configManager.meetsPerformanceTargets() ? .green : .orange)
                }
                
                StatusIndicator(
                    title: "Configuration",
                    value: configManager.selectedPreset.rawValue,
                    status: .neutral,
                    icon: "slider.horizontal.3"
                )
                
                StatusIndicator(
                    title: "Sample Rate",
                    value: "\(Int(configManager.currentConfiguration.sampleRate)) Hz",
                    status: .neutral,
                    icon: "waveform.circle"
                )
                
                StatusIndicator(
                    title: "Buffer Size",
                    value: "\(Int(configManager.currentConfiguration.bufferSize * 1000)) ms",
                    status: .neutral,
                    icon: "timer"
                )
                
                StatusIndicator(
                    title: "Correlation Threshold",
                    value: String(format: "%.2f", configManager.currentConfiguration.correlationThreshold),
                    status: .neutral,
                    icon: "target"
                )
            }
        }
    }
    
    // MARK: - Recommendations Section
    
    private var recommendationsSection: some View {
        ParameterSection(title: "Performance Recommendations", icon: "lightbulb") {
            VStack(spacing: 12) {
                ForEach(performanceRecommendations, id: \.title) { recommendation in
                    RecommendationCard(recommendation: recommendation)
                }
                
                if performanceRecommendations.isEmpty {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        
                        Text("Performance is optimal - no recommendations")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                    }
                    .padding(.vertical, 8)
                }
            }
        }
    }
    
    // MARK: - Helper Properties
    
    private var performanceRecommendations: [PerformanceRecommendation] {
        var recommendations: [PerformanceRecommendation] = []
        let metrics = configManager.performanceMetrics
        let config = configManager.currentConfiguration
        
        // High CPU usage
        if metrics.cpuUsage > 0.7 {
            recommendations.append(PerformanceRecommendation(
                title: "High CPU Usage",
                description: "Consider increasing buffer size or enabling downsampling",
                severity: .warning,
                action: "Optimize CPU"
            ))
        }
        
        // High latency
        if metrics.averageLatency > 0.2 {
            recommendations.append(PerformanceRecommendation(
                title: "High Latency",
                description: "Reduce buffer size for lower latency",
                severity: .warning,
                action: "Reduce Latency"
            ))
        }
        
        // Low detection rate
        if metrics.detectionRate < 0.8 {
            recommendations.append(PerformanceRecommendation(
                title: "Low Detection Rate",
                description: "Lower correlation threshold or adjust frequency range",
                severity: .error,
                action: "Improve Detection"
            ))
        }
        
        // Buffer overruns
        if metrics.bufferOverruns > 0 {
            recommendations.append(PerformanceRecommendation(
                title: "Buffer Overruns",
                description: "Increase buffer size or reduce processing complexity",
                severity: .error,
                action: "Fix Overruns"
            ))
        }
        
        return recommendations
    }
    
    // MARK: - Helper Methods
    
    private func startPerformanceMonitoring() {
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            // Trigger UI refresh by accessing published properties
            _ = configManager.performanceMetrics
        }
    }
    
    private func stopPerformanceMonitoring() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }
}

// MARK: - Performance Recommendation

struct PerformanceRecommendation {
    let title: String
    let description: String
    let severity: Severity
    let action: String
    
    enum Severity {
        case info, warning, error
        
        var color: Color {
            switch self {
            case .info: return .blue
            case .warning: return .orange
            case .error: return .red
            }
        }
        
        var icon: String {
            switch self {
            case .info: return "info.circle"
            case .warning: return "exclamationmark.triangle"
            case .error: return "xmark.circle"
            }
        }
    }
}

// MARK: - Recommendation Card

struct RecommendationCard: View {
    let recommendation: PerformanceRecommendation
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: recommendation.severity.icon)
                .font(.title3)
                .foregroundColor(recommendation.severity.color)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(recommendation.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(recommendation.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button(recommendation.action) {
                // TODO: Implement quick fix actions
            }
            .buttonStyle(.bordered)
            .foregroundColor(recommendation.severity.color)
            .font(.caption)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(recommendation.severity.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(recommendation.severity.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

#Preview {
    PerformanceMonitorView(configManager: ConfigurationManager())
}
