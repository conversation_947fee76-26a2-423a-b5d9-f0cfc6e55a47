//
//  DeveloperSettingsView.swift
//  Breath2
//
//  Created by Mo on 02/06/2025.
//
//  Development-only UI for testing and adjusting algorithm parameters

import SwiftUI

struct DeveloperSettingsView: View {
    @StateObject private var configManager = ConfigurationManager()
    @State private var selectedTab = 0
    @State private var showingExportSheet = false
    @State private var showingImportSheet = false
    @State private var exportedConfig = ""
    @State private var importConfig = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Algorithm Parameters Tab
            AlgorithmParametersView(configManager: configManager)
                .tabItem {
                    Image(systemName: "waveform.path.ecg")
                    Text("Algorithm")
                }
                .tag(0)
            
            // Audio Parameters Tab
            AudioParametersView(configManager: configManager)
                .tabItem {
                    Image(systemName: "mic.fill")
                    Text("Audio")
                }
                .tag(1)
            
            // Performance Monitor Tab
            PerformanceMonitorView(configManager: configManager)
                .tabItem {
                    Image(systemName: "speedometer")
                    Text("Performance")
                }
                .tag(2)
            
            // Preset Manager Tab
            PresetManagerView(configManager: configManager)
                .tabItem {
                    Image(systemName: "slider.horizontal.3")
                    Text("Presets")
                }
                .tag(3)
        }
        .navigationTitle("Developer Settings")
        .navigationBarTitleDisplayMode(.large)
        .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
        .toolbar {
            ToolbarItemGroup(placement: .navigationBarTrailing) {
                Menu {
                    Button("Export Configuration") {
                        exportConfiguration()
                    }
                    
                    Button("Import Configuration") {
                        showingImportSheet = true
                    }
                    
                    Divider()
                    
                    Button("Reset to Default") {
                        resetToDefault()
                    }
                } label: {
                    Image(systemName: "ellipsis.circle")
                        .foregroundColor(.cyan)
                }
            }
        }
        .sheet(isPresented: $showingExportSheet) {
            ExportConfigurationSheet(configString: exportedConfig)
        }
        .sheet(isPresented: $showingImportSheet) {
            ImportConfigurationSheet(
                configString: $importConfig,
                onImport: { importConfiguration() }
            )
        }
        .alert("Configuration", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    // MARK: - Helper Methods
    
    private func exportConfiguration() {
        exportedConfig = configManager.exportConfiguration()
        showingExportSheet = true
    }
    
    private func importConfiguration() {
        if configManager.importConfiguration(from: importConfig) {
            alertMessage = "Configuration imported successfully!"
            importConfig = ""
            showingImportSheet = false
        } else {
            alertMessage = "Failed to import configuration. Please check the format."
        }
        showingAlert = true
    }
    
    private func resetToDefault() {
        configManager.applyPreset(.standard)
        alertMessage = "Configuration reset to default values."
        showingAlert = true
    }
}

// MARK: - Export Configuration Sheet

struct ExportConfigurationSheet: View {
    let configString: String
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Export Configuration")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .padding()
                
                Text("Copy the configuration below to share or backup your settings:")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                ScrollView {
                    Text(configString)
                        .font(.system(.caption, design: .monospaced))
                        .padding()
                        .background(Color.secondary.opacity(0.1))
                        .cornerRadius(8)
                        .textSelection(.enabled)
                }
                .padding()
                
                Button("Copy to Clipboard") {
                    UIPasteboard.general.string = configString
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
                .padding()
            }
            .navigationTitle("Export")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Import Configuration Sheet

struct ImportConfigurationSheet: View {
    @Binding var configString: String
    let onImport: () -> Void
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Import Configuration")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .padding()
                
                Text("Paste a configuration string below to import settings:")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                TextEditor(text: $configString)
                    .font(.system(.caption, design: .monospaced))
                    .padding()
                    .background(Color.secondary.opacity(0.1))
                    .cornerRadius(8)
                    .frame(minHeight: 200)
                    .padding()
                
                Button("Import Configuration") {
                    onImport()
                }
                .buttonStyle(.borderedProminent)
                .disabled(configString.isEmpty)
                .padding()
                
                Spacer()
            }
            .navigationTitle("Import")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    NavigationView {
        DeveloperSettingsView()
    }
}
