//
//  DebugLoggerView.swift
//  Breath2
//
//  Created by Debug System on 19/07/2025.
//

import SwiftUI

struct DebugLoggerView: View {
    @ObservedObject private var debugLogger = DebugLogger.shared
    @State private var showingLogFiles = false
    @State private var selectedLogFile: String?
    @State private var showingShareSheet = false
    @State private var logFiles: [String] = []
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                headerSection
                loggingControlSection
                logFilesSection
                instructionsSection
            }
            .padding()
        }
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.05, green: 0.05, blue: 0.08),
                    Color(red: 0.08, green: 0.08, blue: 0.12),
                    Color(red: 0.10, green: 0.10, blue: 0.15)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .onAppear {
            refreshLogFiles()
        }
        .sheet(isPresented: $showingShareSheet) {
            if let selectedFile = selectedLogFile {
                ShareLogFileView(logFileName: selectedFile)
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "ladybug.fill")
                    .font(.title2)
                    .foregroundColor(.cyan)
                
                Text("Debug Logger")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            Text("Comprehensive logging for breath detection and pitch detection debugging")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
        .padding()
        .background(Color.white.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var loggingControlSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "record.circle")
                    .foregroundColor(.cyan)
                Text("Logging Control")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
            }
            
            VStack(spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Status")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            Circle()
                                .fill(debugLogger.isLogging ? Color.green : Color.gray)
                                .frame(width: 8, height: 8)
                            
                            Text(debugLogger.isLogging ? "Recording" : "Stopped")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                        }
                    }
                    
                    Spacer()
                    
                    if debugLogger.isLogging {
                        VStack(alignment: .trailing, spacing: 4) {
                            Text("Entries")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Text("\(debugLogger.logEntriesCount)")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.cyan)
                        }
                    }
                }
                
                if debugLogger.isLogging && !debugLogger.currentLogFile.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Current Log File")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text(debugLogger.currentLogFile)
                            .font(.system(.caption, design: .monospaced))
                            .foregroundColor(.cyan)
                            .padding(8)
                            .background(Color.white.opacity(0.05))
                            .cornerRadius(6)
                    }
                }
                
                HStack(spacing: 12) {
                    Button(action: {
                        if debugLogger.isLogging {
                            debugLogger.stopLogging()
                        } else {
                            debugLogger.startLogging()
                        }
                    }) {
                        HStack {
                            Image(systemName: debugLogger.isLogging ? "stop.circle.fill" : "record.circle.fill")
                            Text(debugLogger.isLogging ? "Stop Logging" : "Start Logging")
                        }
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(debugLogger.isLogging ? Color.red : Color.green)
                        .cornerRadius(8)
                    }

                    // Test button (only show when logging is active)
                    if debugLogger.isLogging {
                        Button(action: {
                            debugLogger.logTestEntry()
                        }) {
                            HStack {
                                Image(systemName: "testtube.2")
                                Text("Add Test Entry")
                            }
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(Color.blue)
                            .cornerRadius(8)
                        }
                    }

                    Spacer()
                }
            }
        }
        .padding()
        .background(Color.white.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var logFilesSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "doc.text")
                    .foregroundColor(.cyan)
                Text("Log Files")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
                
                Button("Refresh") {
                    refreshLogFiles()
                }
                .font(.subheadline)
                .foregroundColor(.cyan)
            }
            
            if logFiles.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "doc.text.magnifyingglass")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    
                    Text("No log files found")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("Start logging to create debug files")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.white.opacity(0.03))
                .cornerRadius(8)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(logFiles, id: \.self) { logFile in
                        LogFileRow(
                            fileName: logFile,
                            onShare: {
                                selectedLogFile = logFile
                                showingShareSheet = true
                            }
                        )
                    }
                }
            }
        }
        .padding()
        .background(Color.white.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var instructionsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.cyan)
                Text("Instructions")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 12) {
                InstructionRow(
                    number: "1",
                    title: "Start Logging",
                    description: "Tap 'Start Logging' to begin recording debug data"
                )
                
                InstructionRow(
                    number: "2",
                    title: "Use the App",
                    description: "Perform breath detection activities to generate debug data"
                )
                
                InstructionRow(
                    number: "3",
                    title: "Stop & Share",
                    description: "Stop logging and share the log file for analysis"
                )
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Log Location:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                    
                    Text(debugLogger.getLogDirectoryPath())
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.secondary)
                        .padding(8)
                        .background(Color.white.opacity(0.05))
                        .cornerRadius(6)
                }
            }
        }
        .padding()
        .background(Color.white.opacity(0.05))
        .cornerRadius(12)
    }
    
    private func refreshLogFiles() {
        logFiles = debugLogger.getLogFiles()
    }
}

struct LogFileRow: View {
    let fileName: String
    let onShare: () -> Void

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(fileName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)

                HStack {
                    Text(formatFileDate(fileName))
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    // Show file existence status
                    HStack(spacing: 4) {
                        Circle()
                            .fill(DebugLogger.shared.logFileExists(fileName: fileName) ? Color.green : Color.red)
                            .frame(width: 6, height: 6)

                        Text(DebugLogger.shared.logFileExists(fileName: fileName) ? "Available" : "Missing")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Spacer()

            HStack(spacing: 8) {
                // Copy to clipboard button
                Button(action: {
                    copyLogToClipboard(fileName: fileName)
                }) {
                    Image(systemName: "doc.on.clipboard")
                        .font(.subheadline)
                        .foregroundColor(.orange)
                }

                // Share button
                Button(action: {
                    print("🐛 Share button tapped for: \(fileName)")
                    onShare()
                }) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.subheadline)
                        .foregroundColor(.cyan)
                }
            }
        }
        .padding()
        .background(Color.white.opacity(0.03))
        .cornerRadius(8)
    }

    private func copyLogToClipboard(fileName: String) {
        let logFileURL = DebugLogger.shared.getLogFileURL(fileName: fileName)

        do {
            let logContent = try String(contentsOf: logFileURL, encoding: .utf8)
            UIPasteboard.general.string = logContent
            print("✅ Log content copied to clipboard: \(fileName)")
        } catch {
            print("❌ Failed to copy log to clipboard: \(error)")
            UIPasteboard.general.string = "Error reading log file: \(fileName) - \(error.localizedDescription)"
        }
    }

    private func formatFileDate(_ fileName: String) -> String {
        // Extract date from filename format: breath2_debug_yyyy-MM-dd_HH-mm-ss.log
        let components = fileName.replacingOccurrences(of: "breath2_debug_", with: "")
            .replacingOccurrences(of: ".log", with: "")
            .replacingOccurrences(of: "_", with: " ")
            .replacingOccurrences(of: "-", with: ":")
        
        return components
    }
}

struct InstructionRow: View {
    let number: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text(number)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(Color.cyan)
                .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

struct ShareLogFileView: UIViewControllerRepresentable {
    let logFileName: String

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let logFileURL = DebugLogger.shared.getLogFileURL(fileName: logFileName)

        print("🐛 Attempting to share log file: \(logFileName)")
        print("🐛 Full path: \(logFileURL.path)")
        print("🐛 File exists: \(DebugLogger.shared.logFileExists(fileName: logFileName))")

        // Always share as text content to avoid file access issues
        var activityItems: [Any] = []

        if DebugLogger.shared.logFileExists(fileName: logFileName) {
            do {
                let logContent = try String(contentsOf: logFileURL, encoding: .utf8)
                print("🐛 Successfully read log file, content length: \(logContent.count)")

                // Share as text content with a clear title
                let shareTitle = "Breath2 Debug Log - \(logFileName)"
                let shareContent = """
                \(shareTitle)
                Generated: \(Date())

                \(logContent)
                """

                activityItems = [shareContent]
            } catch {
                print("❌ Failed to read log file: \(error)")
                let errorContent = """
                Breath2 Debug Log - \(logFileName)
                Error: Failed to read log file

                Details: \(error.localizedDescription)
                Path: \(logFileURL.path)
                """
                activityItems = [errorContent]
            }
        } else {
            print("❌ Log file not found at: \(logFileURL.path)")
            let notFoundContent = """
            Breath2 Debug Log - \(logFileName)
            Error: File not found

            Path: \(logFileURL.path)
            Available files: \(DebugLogger.shared.getLogFiles().joined(separator: ", "))
            """
            activityItems = [notFoundContent]
        }

        let activityViewController = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: nil
        )

        // Configure for iPad
        if let popover = activityViewController.popoverPresentationController {
            popover.sourceView = UIView()
            popover.sourceRect = CGRect(x: 0, y: 0, width: 1, height: 1)
        }

        return activityViewController
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // No updates needed
    }
}

#Preview {
    DebugLoggerView()
}
