//
//  DebugLoggerView.swift
//  Breath2
//
//  Created by Debug System on 19/07/2025.
//

import SwiftUI

struct DebugLoggerView: View {
    @ObservedObject private var debugLogger = DebugLogger.shared
    @State private var showingLogFiles = false
    @State private var selectedLogFile: String?
    @State private var showingShareSheet = false
    @State private var logFiles: [String] = []
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                headerSection
                loggingControlSection
                logFilesSection
                instructionsSection
            }
            .padding()
        }
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.05, green: 0.05, blue: 0.08),
                    Color(red: 0.08, green: 0.08, blue: 0.12),
                    Color(red: 0.10, green: 0.10, blue: 0.15)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .onAppear {
            refreshLogFiles()
        }
        .sheet(isPresented: $showingShareSheet) {
            if let selectedFile = selectedLogFile {
                ShareLogFileView(logFileName: selectedFile)
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "ladybug.fill")
                    .font(.title2)
                    .foregroundColor(.cyan)
                
                Text("Debug Logger")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            Text("Comprehensive logging for breath detection and pitch detection debugging")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
        .padding()
        .background(Color.white.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var loggingControlSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "record.circle")
                    .foregroundColor(.cyan)
                Text("Logging Control")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
            }
            
            VStack(spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Status")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            Circle()
                                .fill(debugLogger.isLogging ? Color.green : Color.gray)
                                .frame(width: 8, height: 8)
                            
                            Text(debugLogger.isLogging ? "Recording" : "Stopped")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                        }
                    }
                    
                    Spacer()
                    
                    if debugLogger.isLogging {
                        VStack(alignment: .trailing, spacing: 4) {
                            Text("Entries")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Text("\(debugLogger.logEntriesCount)")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.cyan)
                        }
                    }
                }
                
                if debugLogger.isLogging && !debugLogger.currentLogFile.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Current Log File")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text(debugLogger.currentLogFile)
                            .font(.system(.caption, design: .monospaced))
                            .foregroundColor(.cyan)
                            .padding(8)
                            .background(Color.white.opacity(0.05))
                            .cornerRadius(6)
                    }
                }
                
                HStack(spacing: 12) {
                    Button(action: {
                        if debugLogger.isLogging {
                            debugLogger.stopLogging()
                        } else {
                            debugLogger.startLogging()
                        }
                    }) {
                        HStack {
                            Image(systemName: debugLogger.isLogging ? "stop.circle.fill" : "record.circle.fill")
                            Text(debugLogger.isLogging ? "Stop Logging" : "Start Logging")
                        }
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(debugLogger.isLogging ? Color.red : Color.green)
                        .cornerRadius(8)
                    }
                    
                    Spacer()
                }
            }
        }
        .padding()
        .background(Color.white.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var logFilesSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "doc.text")
                    .foregroundColor(.cyan)
                Text("Log Files")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
                
                Button("Refresh") {
                    refreshLogFiles()
                }
                .font(.subheadline)
                .foregroundColor(.cyan)
            }
            
            if logFiles.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "doc.text.magnifyingglass")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    
                    Text("No log files found")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("Start logging to create debug files")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.white.opacity(0.03))
                .cornerRadius(8)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(logFiles, id: \.self) { logFile in
                        LogFileRow(
                            fileName: logFile,
                            onShare: {
                                selectedLogFile = logFile
                                showingShareSheet = true
                            }
                        )
                    }
                }
            }
        }
        .padding()
        .background(Color.white.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var instructionsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.cyan)
                Text("Instructions")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 12) {
                InstructionRow(
                    number: "1",
                    title: "Start Logging",
                    description: "Tap 'Start Logging' to begin recording debug data"
                )
                
                InstructionRow(
                    number: "2",
                    title: "Use the App",
                    description: "Perform breath detection activities to generate debug data"
                )
                
                InstructionRow(
                    number: "3",
                    title: "Stop & Share",
                    description: "Stop logging and share the log file for analysis"
                )
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Log Location:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                    
                    Text(debugLogger.getLogDirectoryPath())
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.secondary)
                        .padding(8)
                        .background(Color.white.opacity(0.05))
                        .cornerRadius(6)
                }
            }
        }
        .padding()
        .background(Color.white.opacity(0.05))
        .cornerRadius(12)
    }
    
    private func refreshLogFiles() {
        logFiles = debugLogger.getLogFiles()
    }
}

struct LogFileRow: View {
    let fileName: String
    let onShare: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(fileName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(formatFileDate(fileName))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button(action: onShare) {
                Image(systemName: "square.and.arrow.up")
                    .font(.subheadline)
                    .foregroundColor(.cyan)
            }
        }
        .padding()
        .background(Color.white.opacity(0.03))
        .cornerRadius(8)
    }
    
    private func formatFileDate(_ fileName: String) -> String {
        // Extract date from filename format: breath2_debug_yyyy-MM-dd_HH-mm-ss.log
        let components = fileName.replacingOccurrences(of: "breath2_debug_", with: "")
            .replacingOccurrences(of: ".log", with: "")
            .replacingOccurrences(of: "_", with: " ")
            .replacingOccurrences(of: "-", with: ":")
        
        return components
    }
}

struct InstructionRow: View {
    let number: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text(number)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(Color.cyan)
                .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

struct ShareLogFileView: UIViewControllerRepresentable {
    let logFileName: String
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let logDirectory = DebugLogger.shared.getLogDirectoryPath()
        let logFileURL = URL(fileURLWithPath: logDirectory).appendingPathComponent(logFileName)
        
        let activityViewController = UIActivityViewController(
            activityItems: [logFileURL],
            applicationActivities: nil
        )
        
        return activityViewController
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // No updates needed
    }
}

#Preview {
    DebugLoggerView()
}
