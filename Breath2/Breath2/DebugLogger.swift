//
//  DebugLogger.swift
//  Breath2
//
//  Created by Debug System on 19/07/2025.
//

import Foundation
import SwiftUI

/// Comprehensive debug logging system for breath detection and pitch detection analysis
class DebugLogger: ObservableObject {
    
    // MARK: - Singleton
    static let shared = DebugLogger()
    
    // MARK: - Published Properties
    @Published var isLogging: Bool = false
    @Published var currentLogFile: String = ""
    @Published var logEntriesCount: Int = 0
    
    // MARK: - Private Properties
    private let documentsDirectory: URL
    private let debugDirectory: URL
    private var currentLogFileURL: URL?
    private let logQueue = DispatchQueue(label: "com.breath2.debuglogger", qos: .utility)
    private var logBuffer: [String] = []
    private let maxBufferSize = 100
    
    // MARK: - Log Entry Types
    enum LogType: String, CaseIterable {
        case pitchDetection = "PITCH"
        case breathDetection = "BREATH"
        case audioProcessing = "AUDIO"
        case pressureCalculation = "PRESSURE"
        case stateTransition = "STATE"
        case performance = "PERF"
        case error = "ERROR"
        case warning = "WARN"
        case info = "INFO"
    }
    
    // MARK: - Data Structures
    struct PitchDetectionLog: Codable {
        let timestamp: Date
        let audioSamples: Int
        let audioLevel: Float
        let audioVariance: Float
        let detectedPitch: Float
        let correlation: Float
        let searchStrategy: String
        let targetMinFreq: Float
        let targetMaxFreq: Float
        let downsampledSamples: Int
        let bestPeriod: Int
        let movingAvePeriod: Float
        let movingAveAmplitude: Float
        let runLength: Int
        let economicalSearchUsed: Bool
        let processingTimeMs: Double
    }
    
    struct BreathDetectionLog: Codable {
        let timestamp: Date
        let currentState: String
        let hasOscillation: Bool
        let audioLevel: Float
        let pressure: Float
        let frequency: Float
        let breathDuration: TimeInterval
        let isBreathing: Bool
        let breathCount: Int
        let amplitudeHistory: [Float]
        let timeSinceLastOscillation: TimeInterval?
        let timeSinceBreathStart: TimeInterval?
        let stateTransitionReason: String?
    }
    
    struct AudioProcessingLog: Codable {
        let timestamp: Date
        let inputSamples: Int
        let filteredSamples: Int
        let squaredSamples: Int
        let downsampledSamples: Int
        let smoothedSamples: Int
        let maxAudioLevel: Float
        let minAudioLevel: Float
        let averageAudioLevel: Float
        let processingStepTimeMs: [String: Double]
    }
    
    // MARK: - Initialization
    private init() {
        documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        debugDirectory = documentsDirectory.appendingPathComponent("DebugLogs")
        
        // Create debug directory if it doesn't exist
        try? FileManager.default.createDirectory(at: debugDirectory, withIntermediateDirectories: true)
        
        print("🐛 DebugLogger initialized - Logs will be saved to: \(debugDirectory.path)")
    }
    
    // MARK: - Public Methods
    
    /// Start logging session with a new log file
    func startLogging() {
        logQueue.async { [weak self] in
            guard let self = self else { return }
            
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
            let timestamp = formatter.string(from: Date())
            let filename = "breath2_debug_\(timestamp).log"
            
            self.currentLogFileURL = self.debugDirectory.appendingPathComponent(filename)
            
            DispatchQueue.main.async {
                self.isLogging = true
                self.currentLogFile = filename
                self.logEntriesCount = 0
            }
            
            // Write header
            let header = """
            ================================================================================
            BREATH2 DEBUG LOG SESSION
            ================================================================================
            Start Time: \(Date())
            Log File: \(filename)
            Purpose: Debug breath detection duration and pitch detection parameters
            ================================================================================
            
            """
            
            self.writeToFile(header)
            self.log(.info, "Debug logging session started", data: ["filename": filename])
        }
    }
    
    /// Stop logging session
    func stopLogging() {
        logQueue.async { [weak self] in
            guard let self = self else { return }

            self.flushBuffer()

            // Generate analysis summary before closing
            self.generateAnalysisSummary()

            let footer = """

            ================================================================================
            DEBUG LOG SESSION ENDED
            ================================================================================
            End Time: \(Date())
            Total Log Entries: \(self.logEntriesCount)
            ================================================================================
            """

            self.writeToFile(footer)

            DispatchQueue.main.async {
                self.isLogging = false
                print("🐛 Debug logging stopped - Log saved to: \(self.currentLogFile)")
            }
        }
    }
    
    /// Log pitch detection data
    func logPitchDetection(
        audioSamples: Int,
        audioLevel: Float,
        audioVariance: Float,
        detectedPitch: Float,
        correlation: Float,
        searchStrategy: String,
        targetMinFreq: Float,
        targetMaxFreq: Float,
        downsampledSamples: Int,
        bestPeriod: Int,
        movingAvePeriod: Float,
        movingAveAmplitude: Float,
        runLength: Int,
        economicalSearchUsed: Bool,
        processingTimeMs: Double
    ) {
        let logData = PitchDetectionLog(
            timestamp: Date(),
            audioSamples: audioSamples,
            audioLevel: audioLevel,
            audioVariance: audioVariance,
            detectedPitch: detectedPitch,
            correlation: correlation,
            searchStrategy: searchStrategy,
            targetMinFreq: targetMinFreq,
            targetMaxFreq: targetMaxFreq,
            downsampledSamples: downsampledSamples,
            bestPeriod: bestPeriod,
            movingAvePeriod: movingAvePeriod,
            movingAveAmplitude: movingAveAmplitude,
            runLength: runLength,
            economicalSearchUsed: economicalSearchUsed,
            processingTimeMs: processingTimeMs
        )
        
        log(.pitchDetection, "Pitch detection analysis", data: logData)
    }
    
    /// Log breath detection data
    func logBreathDetection(
        currentState: String,
        hasOscillation: Bool,
        audioLevel: Float,
        pressure: Float,
        frequency: Float,
        breathDuration: TimeInterval,
        isBreathing: Bool,
        breathCount: Int,
        amplitudeHistory: [Float],
        timeSinceLastOscillation: TimeInterval?,
        timeSinceBreathStart: TimeInterval?,
        stateTransitionReason: String? = nil
    ) {
        let logData = BreathDetectionLog(
            timestamp: Date(),
            currentState: currentState,
            hasOscillation: hasOscillation,
            audioLevel: audioLevel,
            pressure: pressure,
            frequency: frequency,
            breathDuration: breathDuration,
            isBreathing: isBreathing,
            breathCount: breathCount,
            amplitudeHistory: Array(amplitudeHistory.suffix(10)), // Keep last 10 values
            timeSinceLastOscillation: timeSinceLastOscillation,
            timeSinceBreathStart: timeSinceBreathStart,
            stateTransitionReason: stateTransitionReason
        )
        
        log(.breathDetection, "Breath detection analysis", data: logData)
    }
    
    /// Log audio processing steps
    func logAudioProcessing(
        inputSamples: Int,
        filteredSamples: Int,
        squaredSamples: Int,
        downsampledSamples: Int,
        smoothedSamples: Int,
        maxAudioLevel: Float,
        minAudioLevel: Float,
        averageAudioLevel: Float,
        processingStepTimeMs: [String: Double]
    ) {
        let logData = AudioProcessingLog(
            timestamp: Date(),
            inputSamples: inputSamples,
            filteredSamples: filteredSamples,
            squaredSamples: squaredSamples,
            downsampledSamples: downsampledSamples,
            smoothedSamples: smoothedSamples,
            maxAudioLevel: maxAudioLevel,
            minAudioLevel: minAudioLevel,
            averageAudioLevel: averageAudioLevel,
            processingStepTimeMs: processingStepTimeMs
        )
        
        log(.audioProcessing, "Audio processing pipeline", data: logData)
    }
    
    /// Generic log method
    func log<T: Codable>(_ type: LogType, _ message: String, data: T? = nil) {
        guard isLogging else { return }
        
        logQueue.async { [weak self] in
            guard let self = self else { return }
            
            let timestamp = ISO8601DateFormatter().string(from: Date())
            var logEntry = "[\(timestamp)] [\(type.rawValue)] \(message)"
            
            if let data = data {
                do {
                    let encoder = JSONEncoder()
                    encoder.outputFormatting = .prettyPrinted
                    encoder.dateEncodingStrategy = .iso8601
                    let jsonData = try encoder.encode(data)
                    if let jsonString = String(data: jsonData, encoding: .utf8) {
                        logEntry += "\nDATA: \(jsonString)"
                    }
                } catch {
                    logEntry += "\nDATA_ERROR: Failed to encode data - \(error)"
                }
            }
            
            logEntry += "\n" + String(repeating: "-", count: 80) + "\n"
            
            self.logBuffer.append(logEntry)
            
            DispatchQueue.main.async {
                self.logEntriesCount += 1
            }
            
            // Flush buffer if it's getting full
            if self.logBuffer.count >= self.maxBufferSize {
                self.flushBuffer()
            }
        }
    }
    
    /// Get list of available log files
    func getLogFiles() -> [String] {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: debugDirectory, includingPropertiesForKeys: nil)
            return files
                .filter { $0.pathExtension == "log" }
                .map { $0.lastPathComponent }
                .sorted(by: >)
        } catch {
            print("❌ Failed to get log files: \(error)")
            return []
        }
    }
    
    /// Get the full path to the debug logs directory
    func getLogDirectoryPath() -> String {
        return debugDirectory.path
    }

    /// Get the URL for a specific log file
    func getLogFileURL(fileName: String) -> URL {
        return debugDirectory.appendingPathComponent(fileName)
    }

    /// Check if a log file exists
    func logFileExists(fileName: String) -> Bool {
        let fileURL = getLogFileURL(fileName: fileName)
        return FileManager.default.fileExists(atPath: fileURL.path)
    }

    /// Create a test log entry for debugging purposes
    func logTestEntry() {
        struct TestLogData: Codable {
            let timestamp: Date
            let testValue: Double
            let testString: String
            let systemInfo: [String: String]
        }

        let testData = TestLogData(
            timestamp: Date(),
            testValue: 42.0,
            testString: "Debug logging is working!",
            systemInfo: [
                "iOS": UIDevice.current.systemVersion,
                "device": UIDevice.current.model
            ]
        )

        log(.info, "Test log entry created", data: testData)
    }

    /// Generate analysis summary for a completed logging session
    func generateAnalysisSummary() {
        guard !isLogging, currentLogFileURL != nil else { return }

        let analysisContent = """

        ================================================================================
        AUTOMATED ANALYSIS SUMMARY
        ================================================================================

        This log file contains comprehensive debug data for breath detection and pitch detection.

        KEY AREAS TO INVESTIGATE:

        1. PITCH DETECTION ISSUES:
           - Look for entries with [PITCH] tag
           - Check correlation values (should be > 0.6 for valid detection)
           - Examine searchStrategy (economical vs full search)
           - Review processingTimeMs for performance issues
           - Check audioVariance (low values indicate flat signal)

        2. BREATH DETECTION DURATION PROBLEMS:
           - Look for entries with [BREATH] tag
           - Check currentState transitions (idle -> starting -> active -> ending -> completed)
           - Review breathDuration values
           - Examine timeSinceLastOscillation and timeSinceBreathStart
           - Check stateTransitionReason for why states change

        3. AUDIO PROCESSING PIPELINE:
           - Look for entries with [AUDIO] tag
           - Check processingStepTimeMs for bottlenecks
           - Review sample counts at each processing stage
           - Examine audio level statistics (min, max, average)

        4. PRESSURE CALCULATION:
           - Look for entries with [PRESSURE] tag
           - Check relationship between detectedPitch and calculatedPressure
           - Review audioLevel correlation with pressure readings

        COMMON ISSUES TO LOOK FOR:

        - Pitch detection failing (correlation < 0.6): Check audio input quality
        - Breath duration too short/long: Review state transition logic and thresholds
        - High processing times: Check for performance bottlenecks
        - Inconsistent pressure readings: Verify pitch-to-pressure conversion
        - State machine stuck: Look for missing state transitions

        ANALYSIS TIPS:

        1. Filter by log type using tags: [PITCH], [BREATH], [AUDIO], [PRESSURE]
        2. Look for patterns in timestamp sequences
        3. Compare successful vs failed detections
        4. Check for correlation between audio levels and detection success
        5. Review parameter values that lead to good vs poor performance

        ================================================================================
        """

        logQueue.async { [weak self] in
            self?.writeToFile(analysisContent)
        }
    }
    
    // MARK: - Private Methods
    
    private func writeToFile(_ content: String) {
        guard let fileURL = currentLogFileURL else { return }
        
        do {
            if FileManager.default.fileExists(atPath: fileURL.path) {
                let fileHandle = try FileHandle(forWritingTo: fileURL)
                fileHandle.seekToEndOfFile()
                fileHandle.write(content.data(using: .utf8) ?? Data())
                fileHandle.closeFile()
            } else {
                try content.write(to: fileURL, atomically: true, encoding: .utf8)
            }
        } catch {
            print("❌ Failed to write to log file: \(error)")
        }
    }
    
    private func flushBuffer() {
        guard !logBuffer.isEmpty else { return }
        
        let content = logBuffer.joined()
        writeToFile(content)
        logBuffer.removeAll()
    }
}
