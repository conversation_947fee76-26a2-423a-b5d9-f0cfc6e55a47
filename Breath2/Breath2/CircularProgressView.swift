//
//  CircularProgressView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import SwiftUI

struct CircularProgressView: View {
    let progress: Double
    let value: Double
    let maxValue: Double
    let lineWidth: CGFloat
    let size: CGFloat
    
    @State private var animatedProgress: Double = 0
    @State private var animatedValue: Double = 0
    
    init(
        progress: Double,
        value: Double,
        maxValue: Double = 100,
        lineWidth: CGFloat = 12,
        size: CGFloat = 200
    ) {
        self.progress = progress
        self.value = value
        self.maxValue = maxValue
        self.lineWidth = lineWidth
        self.size = size
    }
    
    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .stroke(
                    Color.gray.opacity(0.2),
                    lineWidth: lineWidth
                )
                .frame(width: size, height: size)
            
            // Progress circle
            Circle()
                .trim(from: 0, to: animatedProgress)
                .stroke(
                    progressGradient,
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round
                    )
                )
                .frame(width: size, height: size)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: animatedProgress)
            
            // Center content
            VStack(spacing: 8) {
                // Main value
                Text("\(Int(animatedValue))")
                    .font(.system(size: 48, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                    .contentTransition(.numericText())
                
                // Unit or description
                Text("cm H₂O")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .fontWeight(.medium)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                animatedProgress = progress
                animatedValue = value
            }
        }
        .onChange(of: progress) {
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedProgress = progress
            }
        }
        .onChange(of: value) {
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedValue = value
            }
        }
    }
    
    private var progressGradient: AngularGradient {
        let colors = progressColors
        return AngularGradient(
            gradient: Gradient(colors: colors),
            center: .center,
            startAngle: .degrees(0),
            endAngle: .degrees(360 * progress)
        )
    }
    
    private var progressColors: [Color] {
        let normalizedValue = value / maxValue
        
        if normalizedValue < 0.3 {
            // Low pressure - red to orange
            return [.red, .orange]
        } else if normalizedValue < 0.5 {
            // Medium-low pressure - orange to yellow
            return [.orange, .yellow]
        } else if normalizedValue < 0.8 {
            // Good pressure - green
            return [.green, .mint]
        } else {
            // High pressure - blue to purple
            return [.blue, .purple]
        }
    }
}

// MARK: - Animated Circular Progress with Tick Marks

struct DetailedCircularProgressView: View {
    let progress: Double
    let value: Double
    let targetMin: Double
    let targetMax: Double
    let maxValue: Double
    
    @State private var animatedProgress: Double = 0
    @State private var animatedValue: Double = 0
    
    init(
        progress: Double,
        value: Double,
        targetMin: Double = 10,
        targetMax: Double = 20,
        maxValue: Double = 30
    ) {
        self.progress = progress
        self.value = value
        self.targetMin = targetMin
        self.targetMax = targetMax
        self.maxValue = maxValue
    }
    
    var body: some View {
        ZStack {
            // Background with tick marks
            ForEach(0..<Int(maxValue), id: \.self) { tick in
                Rectangle()
                    .fill(tickColor(for: Double(tick)))
                    .frame(width: 2, height: tickHeight(for: Double(tick)))
                    .offset(y: -90)
                    .rotationEffect(.degrees(Double(tick) * 12)) // 360/30 = 12 degrees per tick
            }
            
            // Target range indicator
            Circle()
                .trim(
                    from: targetMin / maxValue,
                    to: targetMax / maxValue
                )
                .stroke(
                    Color.green.opacity(0.3),
                    style: StrokeStyle(lineWidth: 20, lineCap: .round)
                )
                .frame(width: 180, height: 180)
                .rotationEffect(.degrees(-90))
            
            // Current progress
            Circle()
                .trim(from: 0, to: animatedProgress)
                .stroke(
                    progressGradient,
                    style: StrokeStyle(lineWidth: 8, lineCap: .round)
                )
                .frame(width: 180, height: 180)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: animatedProgress)
            
            // Center display
            VStack(spacing: 4) {
                Text("\(Int(animatedValue))")
                    .font(.system(size: 42, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                    .contentTransition(.numericText())

                Text("cm H₂O")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .fontWeight(.medium)

                // Status indicator
                Circle()
                    .fill(statusColor)
                    .frame(width: 8, height: 8)
                    .scaleEffect(isInTargetRange ? 1.2 : 0.8)
                    .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: isInTargetRange)
            }
        }
        .frame(width: 220, height: 220)
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                animatedProgress = progress
                animatedValue = value
            }
        }
        .onChange(of: progress) {
            withAnimation(.easeInOut(duration: 0.3)) {
                animatedProgress = progress
            }
        }
        .onChange(of: value) {
            withAnimation(.easeInOut(duration: 0.3)) {
                animatedValue = value
            }
        }
    }
    
    private func tickColor(for value: Double) -> Color {
        if value >= targetMin && value <= targetMax {
            return .green
        } else if value < targetMin {
            return .orange
        } else {
            return .red
        }
    }
    
    private func tickHeight(for value: Double) -> CGFloat {
        if value.truncatingRemainder(dividingBy: 5) == 0 {
            return 12 // Major tick
        } else {
            return 6  // Minor tick
        }
    }
    
    private var progressGradient: LinearGradient {
        if isInTargetRange {
            return LinearGradient(
                colors: [.green, .mint],
                startPoint: .leading,
                endPoint: .trailing
            )
        } else if value < targetMin {
            return LinearGradient(
                colors: [.orange, .yellow],
                startPoint: .leading,
                endPoint: .trailing
            )
        } else {
            return LinearGradient(
                colors: [.red, .orange],
                startPoint: .leading,
                endPoint: .trailing
            )
        }
    }
    
    private var statusColor: Color {
        if isInTargetRange {
            return .green
        } else if value < targetMin {
            return .orange
        } else {
            return .red
        }
    }
    
    private var isInTargetRange: Bool {
        value >= targetMin && value <= targetMax
    }
}

// MARK: - Breath-Focused Circular Progress View

struct BreathCircularProgressView: View {
    @ObservedObject var breathDetector: BreathDetector
    let progress: Double
    let currentPressure: Double
    let targetMin: Double
    let targetMax: Double
    let maxValue: Double

    @State private var animatedProgress: Double = 0

    init(
        breathDetector: BreathDetector,
        progress: Double,
        currentPressure: Double = 0,
        targetMin: Double = 10,
        targetMax: Double = 20,
        maxValue: Double = 30
    ) {
        self.breathDetector = breathDetector
        self.progress = progress
        self.currentPressure = currentPressure
        self.targetMin = targetMin
        self.targetMax = targetMax
        self.maxValue = maxValue
    }

    var body: some View {
        ZStack {
            // Background with tick marks
            ForEach(0..<Int(maxValue), id: \.self) { tick in
                Rectangle()
                    .fill(tickColor(for: Double(tick)))
                    .frame(width: 2, height: tickHeight(for: Double(tick)))
                    .offset(y: -90)
                    .rotationEffect(.degrees(Double(tick) * 12)) // 360/30 = 12 degrees per tick
            }

            // Target range indicator
            Circle()
                .trim(
                    from: targetMin / maxValue,
                    to: targetMax / maxValue
                )
                .stroke(
                    Color.green.opacity(0.3),
                    style: StrokeStyle(lineWidth: 20, lineCap: .round)
                )
                .frame(width: 180, height: 180)
                .rotationEffect(.degrees(-90))

            // Current progress with zone-based colors
            Circle()
                .trim(from: 0, to: animatedProgress)
                .stroke(
                    pressureZoneColor,
                    style: StrokeStyle(lineWidth: 8, lineCap: .round)
                )
                .frame(width: 180, height: 180)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 0.5), value: animatedProgress)
                .animation(.easeInOut(duration: 0.3), value: pressureZoneColor)

            // Center display - Breath Status, Counter, and Timer
            VStack(spacing: 6) {
                // Breath count (main display)
                Text("\(breathDetector.currentBreathNumber)")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(breathDetector.isBreathing ? .green : .white)
                    .contentTransition(.numericText())

                Text("Breaths")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .fontWeight(.medium)

                // Current breath timer (if breathing)
                if breathDetector.isBreathing && breathDetector.currentBreathDuration > 0 {
                    Text(formatBreathDuration(breathDetector.currentBreathDuration))
                        .font(.system(size: 16, weight: .semibold, design: .monospaced))
                        .foregroundColor(.cyan)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(Color.cyan.opacity(0.1))
                        )
                }

                // Breath status
                HStack(spacing: 6) {
                    Circle()
                        .fill(statusColor)
                        .frame(width: 8, height: 8)
                        .scaleEffect(breathDetector.isBreathing ? 1.3 : 1.0)
                        .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true),
                                  value: breathDetector.isBreathing)

                    Text(statusText)
                        .font(.caption2)
                        .foregroundColor(.gray)
                }
            }
        }
        .frame(width: 220, height: 220)
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                animatedProgress = progress
            }
        }
        .onChange(of: progress) { _, newValue in
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedProgress = newValue
            }
        }
    }

    private func tickColor(for value: Double) -> Color {
        if value >= targetMin && value <= targetMax {
            return .green.opacity(0.6)
        } else {
            return .gray.opacity(0.3)
        }
    }

    private func tickHeight(for value: Double) -> CGFloat {
        if value.truncatingRemainder(dividingBy: 5) == 0 {
            return 12 // Major tick
        } else {
            return 6  // Minor tick
        }
    }

    private var pressureZoneColor: Color {
        if currentPressure == 0 {
            return .gray.opacity(0.6) // No pressure - neutral gray
        } else if currentPressure < targetMin {
            return .orange // Below target - amber/orange
        } else if currentPressure >= targetMin && currentPressure <= targetMax {
            return .green // In target zone - green
        } else {
            return .red // Above target - red
        }
    }

    private var statusColor: Color {
        switch breathDetector.currentState {
        case .idle: return .gray
        case .starting: return .yellow
        case .active: return .green
        case .ending: return .orange
        case .completed: return .blue
        }
    }

    private var statusText: String {
        switch breathDetector.currentState {
        case .idle: return "Ready"
        case .starting: return "Starting"
        case .active: return "Active"
        case .ending: return "Ending"
        case .completed: return "Done"
        }
    }

    private func formatBreathDuration(_ duration: TimeInterval) -> String {
        return String(format: "%.1fs", duration)
    }
}

#Preview {
    VStack(spacing: 40) {
        CircularProgressView(
            progress: 0.73,
            value: 73,
            maxValue: 100
        )

        DetailedCircularProgressView(
            progress: 0.6,
            value: 18,
            targetMin: 10,
            targetMax: 20,
            maxValue: 30
        )

        BreathCircularProgressView(
            breathDetector: BreathDetector(),
            progress: 0.6,
            currentPressure: 18
        )
    }
    .padding()
}
