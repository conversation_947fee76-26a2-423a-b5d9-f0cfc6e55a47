# Debug Logging System for Breath2

## Overview

The Breath2 app now includes a comprehensive debug logging system to help diagnose issues with breath detection duration and pitch detection parameters. This system captures detailed data about the audio processing pipeline, pitch detection algorithm, breath state machine, and pressure calculations.

## How to Use

### 1. Access Debug Logger
- Open the Breath2 app
- Go to **Settings** tab
- Tap **Developer Settings** (only available in DEBUG builds)
- Select the **Debug Logger** tab

### 2. Start Logging
- Tap **"Start Logging"** to begin recording debug data
- The system will create a new log file with timestamp
- You'll see the logging status change to "Recording"

### 3. Generate Debug Data
- Use the app normally to perform breath detection activities
- Try different breathing patterns and intensities
- The system will automatically log:
  - Audio processing steps and timing
  - Pitch detection results and parameters
  - Breath state transitions and durations
  - Pressure calculations
  - Performance metrics

### 4. Stop Logging and Export
- Tap **"Stop Logging"** when you've captured enough data
- The system will automatically generate an analysis summary
- Tap the share button next to your log file to export it
- Share the log file via email, AirDrop, or save to Files

## Log File Location

Debug logs are saved to:
```
/Documents/DebugLogs/breath2_debug_YYYY-MM-DD_HH-MM-SS.log
```

## What Gets Logged

### Pitch Detection Data
- Audio sample counts and processing times
- Audio level statistics (min, max, average, variance)
- Correlation values and search strategies
- Target frequency ranges
- Moving averages and run lengths
- Processing step timing (filtering, squaring, downsampling, smoothing, autocorrelation)

### Breath Detection Data
- Current state (idle, starting, active, ending, completed)
- Oscillation detection status
- Audio level, pressure, and frequency readings
- Breath duration and count
- Amplitude history
- Time since last oscillation and breath start
- State transition reasons

### Audio Processing Pipeline
- Sample counts at each processing stage
- Audio level statistics
- Processing step timing breakdown
- Buffer management information

### Pressure Calculation
- Detected pitch values
- Calculated pressure readings
- Audio level correlation
- Frame count information

## Analyzing Log Files

Each log file includes:

1. **Header Section**: Session information and metadata
2. **Debug Entries**: Timestamped entries with detailed data in JSON format
3. **Analysis Summary**: Automated analysis guide with common issues to look for
4. **Footer Section**: Session completion information

### Log Entry Format
```
[TIMESTAMP] [TYPE] MESSAGE
DATA: {JSON formatted data}
--------------------------------------------------------------------------------
```

### Log Types
- `[PITCH]` - Pitch detection analysis
- `[BREATH]` - Breath detection analysis  
- `[AUDIO]` - Audio processing pipeline
- `[PRESSURE]` - Pressure calculation
- `[STATE]` - State transitions
- `[PERF]` - Performance metrics
- `[ERROR]` - Error conditions
- `[WARN]` - Warning conditions
- `[INFO]` - General information

## Common Issues to Look For

### Pitch Detection Problems
- **Low correlation values** (< 0.6): Poor audio input quality
- **High processing times**: Performance bottlenecks
- **Inconsistent frequency detection**: Parameter tuning needed
- **Search strategy failures**: Economical vs full search issues

### Breath Detection Duration Issues
- **Stuck state machine**: Missing state transitions
- **Too short durations**: Minimum duration threshold too high
- **Too long durations**: Maximum silence gap too long
- **False positives**: Oscillation threshold too low
- **Missed breaths**: Oscillation threshold too high

### Audio Processing Issues
- **Low audio variance**: Flat signal, check microphone
- **High processing times**: Performance optimization needed
- **Sample count mismatches**: Buffer management issues
- **Audio level problems**: Input gain or sensitivity issues

## Tips for Effective Debugging

1. **Start with a clean session**: Reset the app before logging
2. **Test specific scenarios**: Focus on problematic breathing patterns
3. **Compare good vs bad sessions**: Log both working and failing cases
4. **Check parameter relationships**: Look for correlations between settings and performance
5. **Monitor timing**: Identify performance bottlenecks
6. **Review state transitions**: Understand breath detection flow

## Technical Details

### Performance Impact
- Logging has minimal performance impact when stopped
- Active logging adds ~1-2ms per audio frame
- Log files are buffered and written asynchronously
- Automatic cleanup prevents storage bloat

### Data Privacy
- All data is stored locally on device
- No network transmission of debug data
- User controls when logging starts/stops
- Log files can be deleted manually

### File Management
- Automatic log file rotation
- Timestamped filenames for easy identification
- Built-in sharing functionality
- Local storage in app's Documents directory

## Support

When reporting issues, please include:
1. Debug log file from problematic session
2. Description of expected vs actual behavior
3. Device model and iOS version
4. App version and build number
5. Steps to reproduce the issue

The debug log will help identify:
- Parameter values that need adjustment
- Performance bottlenecks
- State machine issues
- Audio processing problems
- Algorithm tuning opportunities
